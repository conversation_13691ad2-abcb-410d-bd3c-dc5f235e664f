E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\MaxRectsGlyphPacker.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\MaxRectsGlyphPacker.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\UniGlyphPacker.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniGlyphPacker.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniSDF_Renderer.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniSDF_Renderer.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniFT_Renderer.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniFT_Renderer.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\UniPostProcessor.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniPostProcessor.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\SDFGenerator.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\SDFGenerator.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Windows\WindowsFontFallback.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\WindowsFontFallback.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Android\AndroidFontFallback.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\AndroidFontFallback.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Apple\AppleFontFallback.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\AppleFontFallback.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Default\DefaultFontFallback.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\DefaultFontFallback.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\Unicode\Unicode.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\Unicode.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\FreeType\UniFontFreeType.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniFontFreeType.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\TextShaping\UniTextShaper.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniTextShaper.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextProcessor.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniTextProcessor.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Custom\UniCustomFont.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniCustomFont.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFont.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniFont.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFontAtlasEntry.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniFontAtlasEntry.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFontFallback.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniFontFallback.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniGlyphData.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniGlyphData.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniPlatform.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniPlatform.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniTexture.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniGUID.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniGUID.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniObject.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniObject.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextAPIExport.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniTextAPIExport.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniTextGenerator.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGlobal.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Debug\UniTextGlobal.obj
