#include "UniFontFreeType.h"

#if USE_FREETYPE

//#include <freetype/ftmodapi.h>
#include <freetype/ftrender.h>
#include <freetype/ftbitmap.h>
#include <freetype/ftoutln.h>
#include <algorithm>
#include <cctype>

NAMESPACE_USE

//////////////////////////////////////////////
/// statics & consts & globals
//////////////////////////////////////////////
static std::array<uint8, kThreadNum> s_FTLibraryRef = { 0 };
//static std::array<FT_Library, kThreadNum> s_FTLibraryList{ nullptr };

static std::array<FreeTypeContext, kThreadNum> s_FTContext{};

#if USE_FREETYPE_MEMORY
/// <summary>
/// Custom Memory Allocation for FreeType
/// </summary>
void* uni_ft_alloc(FT_Memory memory, long size) {
    auto context = static_cast<FreeTypeContext*>(memory->user);
    if (context != nullptr)
        return context->Alloc(size);

    return nullptr;
}
void uni_ft_free(FT_Memory memory, void* block) {
    auto context = static_cast<FreeTypeContext*>(memory->user);
    if (context != nullptr)
        context->Free(block);
}
void* uni_ft_realloc(FT_Memory memory, long cur_size, long new_size, void* block) {
    auto context = static_cast<FreeTypeContext*>(memory->user);
    if (context != nullptr)
        return context->Realloc(cur_size, new_size, block);

    return nullptr;
}
#endif

//////////////////////////////////////////////
/// FreeTypeContext
//////////////////////////////////////////////
bool FreeTypeContext::Initialize() noexcept
{
    FT_Error error{ FT_Err_Ok };
    if (ft_library == nullptr)
    {
#if USE_FREETYPE_MEMORY
        ft_memory = new FT_MemoryRec_();
        {
            ft_memory->user = this;
            ft_memory->alloc = uni_ft_alloc;
            ft_memory->realloc = uni_ft_realloc;
            ft_memory->free = uni_ft_free;
        }
        error = FT_New_Library(ft_memory, &ft_library);
        if (!error)
        {
            FT_Add_Default_Modules(ft_library);
        }

        FT_Set_Default_Properties(ft_library);
#else
        FT_Init_FreeType(&ft_library);
#endif
    }

    ++ref_cnt;

    return error == FT_Err_Ok;
}

void FreeTypeContext::Release() noexcept
{
    if (--ref_cnt == 0)
    {
#if USE_FREETYPE_MEMORY
        if (ft_library != nullptr) { FT_Done_Library(ft_library); }
        if (ft_memory != nullptr) { delete ft_memory; ft_memory = nullptr; }
#else
        FT_Done_FreeType(ft_library);
#endif
    }
}

//////////////////////////////////////////////
/// UniFontFreeType
//////////////////////////////////////////////
UniFontFreeType::UniFontFreeType()
{
    // Initialize any FreeType-specific resources
}

UniFontFreeType::~UniFontFreeType() noexcept
{
    ReleaseFTFace();

    //if ((--s_FTLibraryRef) <= 0)
    //    FT_Done_FreeType(s_FTLibrary);
}

void UniFontFreeType::ReleaseFTFace()
{
    for (int i = 0; i < kThreadNum; i++)
    {
        if (m_FTFace[i] != nullptr)
        {
            FT_Done_Face(m_FTFace[i]);
            m_FTFace[i] = nullptr;
        }

        s_FTContext[i].Release();

        //if ((--s_FTLibraryRef[i]) <= 0)
        //    FT_Done_FreeType(s_FTLibraryList[i]);
    }
}

bool UniFontFreeType::InitLibrary()
{
    /*
    FT_Error error = FT_Err_Ok;
    if (s_FTLibrary == nullptr)
    {
        error = FT_Init_FreeType(&s_FTLibrary);
    }

    if (error != FT_Err_Ok)
    {
        // TODO: error handler
        //FE_Error_String(error);
        return false;
    }

    return true;
    */

    FT_Error error = FT_Err_Ok;
    for (int i = 0; i < kThreadNum; i++)
    {
        if (!s_FTContext[i].Initialize()) break;
        /*
        // skip already created library
        if (s_FTLibraryList[i] != nullptr) continue;

        error = FT_Init_FreeType(&s_FTLibraryList[i]);
        if (error != FT_Err_Ok)
        {
            break;
        }
        */
    }

    if (error != FT_Err_Ok)
    {
        // TODO: error handler
        //FE_Error_String(error);
        return false;
    }

    return true;
}

bool UniFontFreeType::CreateFace(const char* filePath)
{
    FT_Error error = FT_Err_Ok;

    //if (m_FTFace == nullptr)
    for (int i = 0; i < kThreadNum; i++)
    {
        FT_Face face = nullptr;
        //error = FT_New_Face(s_FTLibraryList[i], filePath, 0, &face);
        error = FT_New_Face(s_FTContext[i].Library(), filePath, 0, &face);
        if (error == FT_Err_Ok)
        {
            m_FTFace[i] = face;
            s_FTLibraryRef[i]++;
        }
        else
        {
            break;
        }
    }

    if (error != FT_Err_Ok)
    {
        ReleaseFTFace();
        return false;
    }

    OnInitialized();
    return true;
}


bool UniFontFreeType::CreateMemoryFace(const unsigned char* fileBytes, unsigned long fileSize)
{
    FT_Error error = FT_Err_Ok;

#if USE_HARFBUZZ
    // Store font data for HarfBuzz integration
    m_FontData = fileBytes;
    m_FontDataSize = fileSize;
#endif

    for (int i = 0; i < kThreadNum; i++)
    {
        FT_Face face = nullptr;
        //error = FT_New_Memory_Face(s_FTLibraryList[i], fileBytes, fileSize, 0, &face);
        error = FT_New_Memory_Face(s_FTContext[i].Library(), fileBytes, fileSize, 0, &face);
        if (error == FT_Err_Ok)
        {
            m_FTFace[i] = face;
            s_FTLibraryRef[i]++;
        }
        else
        {
            break;
        }
    }

    if (error != FT_Err_Ok)
    {
        ReleaseFTFace();
        return false;
    }

    OnInitialized();
    /*
    if (m_FTFace == nullptr)
    {
        error = FT_New_Memory_Face(s_FTLibrary, fileBytes, fileSize, 0, &m_FTFace);
    }

    if (error != FT_Err_Ok)
    {
        // TODO: error handler
        // FE_Error_String(error);
        return false;
    }
    OnInitialized();
    */

    return true;
}

float UniFontFreeType::GetKerning(uint16 first, uint16 second) const noexcept
{
    if (first == 0 || second == 0) return 0.0f;

    if (FT_HAS_KERNING(m_FTFace[0]))
    {
        FT_UInt index1 = FT_Get_Char_Index(m_FTFace[0], first);
        FT_UInt index2 = FT_Get_Char_Index(m_FTFace[0], second);

        // Get the kerning vector
        FT_Vector kerning;
        FT_Get_Kerning(m_FTFace[0], index1, index2, FT_KERNING_DEFAULT, &kerning);

        // X advance is already in pixels for bitmap fonts
        if (!FT_IS_SCALABLE(m_FTFace[0]))
            return static_cast<float>(kerning.x);

        // Return the X advance
        return static_cast<float>(kerning.x) * kInvOfShiftFactor;
    }

    return 0.0f;
}

void UniFontFreeType::SetFontSizeForRendering(int fontSize, RenderMode renderMode)
{
    if (m_RenderFontSize != fontSize)
    {
        m_RenderFontSize = fontSize;
        const int spreadSize = SDFSpreadSize(fontSize);

        for (int i = 0; i < kThreadNum; i++)
        {
            switch (renderMode)
            {
            case RenderMode::SDF:
            {
                // Apply sdf spread
                // TODO: Apply only once it is changed
                // Note that spread size will affect the width & height of a bitmap
                //FT_Property_Set(s_FTLibraryList[i], "sdf", "spread", &spreadSize);
                FT_Property_Set(s_FTContext[i].Library(), "sdf", "spread", &spreadSize);
                break;
            }
            case RenderMode::BSDF:
            {
                FT_Property_Set(s_FTContext[i].Library(), "bsdf", "spread", &spreadSize);
                break;
            }
            case RenderMode::VSDF:
            {
                FT_Property_Set(s_FTContext[i].Library(), "vsdf", "spread", &spreadSize);
                break;
            }
            default:break;
            }
        }
    }
}

// 这个函数做了一些必要的调整
bool UniFontFreeType::LoadGlyph(uint32 charIndex, int fontSize, RenderMode renderMode, GlyphInfo** out, uint32 thread_id)
{
    FT_F26Dot6 charSize = (FT_F26Dot6)(fontSize * (1 << 6));

    // ??: what's the difference: FT_Set_Pixel_Sizes vs FT_Set_Char_Size
    //error = FT_Set_Pixel_Sizes(face, charSize, charSize);
    FT_Set_Char_Size(m_FTFace[thread_id], charSize, charSize, 72, 72); // * (1 << 6) == / 64

    FT_Int32 loadFlags = GetLoadFlags(renderMode);
    FT_Error error = FT_Load_Glyph(m_FTFace[thread_id], charIndex, loadFlags);
    if (error != FT_Err_Ok)
    {
        return false;
    }

    auto slot = m_FTFace[thread_id]->glyph;
    if (renderMode == RenderMode::GSDF)
    {
        //glyphInfo->advance.x = slot->advance.x * kInvOfShiftFactor;
        //glyphInfo->advance.y = slot->advance.y * kInvOfShiftFactor;

        // TODO: glyphInfo->rect is not properly calculated here
        // so GSDF will not work properly as of now
        m_SdfRenderer.PreRenderGlyph(fontSize, renderMode, *out);
        // outline should be inited during LoadGlyph
        error = FT_Outline_Decompose(&slot->outline, &m_OutlineFuncs, &m_SdfRenderer);
    }
    else
    {
        // advances are handled in PreRenderGlyph
        // glyphInfo->advance.x = slot->advance.x * kInvOfShiftFactor;
        // glyphInfo->advance.y = slot->advance.y * kInvOfShiftFactor;
        m_FTRenderer.PreRenderGlyph(fontSize, renderMode, *out);
    }

    if (error != FT_Err_Ok) return false;

    return true;
}

// Temp code for 16 bit render mode
static bool g_bitmap8bppInit{ false };
static FT_Bitmap g_bitmap8bpp;
bool UniFontFreeType::RenderGlyph(uint32 charIndex, int fontSize, RenderMode renderMode, GlyphInfo** out, uint8** buffer, uint32 thread_id)
{
    FT_Error error;

    // Unity use Italic here by matrix, but this will change the rendered bitmap.
    // We will do this later in TextGenerator
    /* The meaning of the matrix equals to: angle = 0.
     * matrix.xx = (FT_Fixed)( cos( angle ) * 0x10000L );
     * matrix.xy = (FT_Fixed)(-sin( angle ) * 0x10000L );
     * matrix.yx = (FT_Fixed)( sin( angle ) * 0x10000L );
     * matrix.yy = (FT_Fixed)( cos( angle ) * 0x10000L ); */
    static FT_Matrix matrix
    {
        (FT_Fixed)(1 * 0x10000L), //xx
        (FT_Fixed)(0 * 0x10000L), //xy
        (FT_Fixed)(0 * 0x10000L), //yx
        (FT_Fixed)(1 * 0x10000L)  //yy
    };
    FT_Set_Transform(m_FTFace[thread_id], &matrix, nullptr);

    auto slot = m_FTFace[thread_id]->glyph;
    FT_Bitmap& bitmap = slot->bitmap;

    // reuse buffer for bitmap
    //auto bufferSize = bitmap.rows * bitmap.pitch;
    //bitmap.buffer = s_FTContext[thread_id].EnsureSharedBuffer(bufferSize);

    (*out)->spreadSize = 0;
    FT_Render_Mode ftRenderMode;
    // More ditails on sdf or bsdf:
    // https://gitlab.freedesktop.org/freetype/freetype-demos/-/blob/master/src/ftsdf.c
    switch (renderMode)
    {
    case RenderMode::SDF:
    {
        (*out)->spreadSize = static_cast<int8>(SDFSpreadSize(fontSize));
        ftRenderMode = FT_RENDER_MODE_SDF;
        break;
    }
    case RenderMode::BSDF:
    {
        (*out)->spreadSize = static_cast<int8>(SDFSpreadSize(fontSize));
        ftRenderMode = FT_RENDER_MODE_SDF;
        // force use bsdf
        FT_Render_Glyph(slot, FT_RENDER_MODE_NORMAL);
        break;
    }
    case RenderMode::CSDF:
    {
        (*out)->spreadSize = static_cast<int8>(SDFSpreadSize(fontSize));
        ftRenderMode = FT_LOAD_TARGET_MODE(GetLoadFlags(renderMode));
        break;
    }
    case RenderMode::VSDF:
    {
        (*out)->spreadSize = static_cast<int8>(SDFSpreadSize(fontSize));
        ftRenderMode = FT_RENDER_MODE_VSDF;
        break;
    }
    case RenderMode::GSDF:
    {
        (*out)->spreadSize = static_cast<int8>(SDFSpreadSize(fontSize));
        error = FT_Outline_Decompose(&slot->outline, &m_OutlineFuncs, &m_SdfRenderer);
        if (error == FT_Err_Ok)
        {
            FT_BBox  cbox, pbox;
            FT_Outline_Get_CBox( &slot->outline, &cbox );

            /* rough estimate of pixel box */
            pbox.xMin = ( cbox.xMin >> 6 ) & 63;
            pbox.yMin = ( cbox.yMin >> 6 ) & 63;
            pbox.xMax = ( cbox.xMax >> 6 ) & 63;
            pbox.yMax = ( cbox.yMax >> 6 ) & 63;

            auto x_left = pbox.xMin - (*out)->spreadSize;
            auto y_top  = pbox.yMax + (*out)->spreadSize;
            auto width  = pbox.xMax - pbox.xMin + ((*out)->spreadSize << 1);
            auto height = pbox.yMax - pbox.yMin + ((*out)->spreadSize << 1);

            (*out)->bounds = GlyphBounds
            {
                static_cast<float>(x_left),
                static_cast<float>(y_top),
                static_cast<float>(width),
                static_cast<float>(height)
            };

            (*out)->rect.width = width;
            (*out)->rect.height = height;
        }

        return error == FT_Err_Ok;
    }
    default:
        ftRenderMode = FT_LOAD_TARGET_MODE(GetLoadFlags(renderMode));
        break;
    }

    error = FT_Render_Glyph(slot, ftRenderMode);
    if (error != FT_Err_Ok)
    {
        return false;
    }

    // bitmap convertion
    uint8 bytesPP = 1;
    FT_Bitmap* srcBitmap = &bitmap;
    switch (bitmap.pixel_mode)
    {
    case FT_PIXEL_MODE_GRAY16:
        bytesPP = 2;
        break;
    case FT_PIXEL_MODE_RGB:
        bytesPP = 3;
        break;
    //case FT_PIXEL_MODE_GRAY:
    default:
        break;
    }

    // Step 5: Post Process goes here
    if (m_LastRenderMode != renderMode)
    {
        m_LastRenderMode = renderMode;
        for (int i = 0; i < kThreadNum; i++)
        {
            m_PostProcessor[i] = CreatePostProcessor(renderMode);
        }
    }

    if (m_PostProcessor[thread_id] != nullptr)
    {
        // in custom sdf gen, we need to add the padding to the bounds
        (*out)->bounds = GlyphBounds
        {
            static_cast<float>(slot->bitmap_left) - (*out)->spreadSize,
            static_cast<float>(slot->bitmap_top) + (*out)->spreadSize,
            static_cast<float>(bitmap.width) + ((*out)->spreadSize << 1),
            static_cast<float>(bitmap.rows) + ((*out)->spreadSize << 1)
        };

        ImageData input
        {
            bytesPP, // bytes per pixel
            static_cast<uint16>(srcBitmap->width),
            static_cast<uint16>(srcBitmap->rows),
            srcBitmap->buffer
        };
        auto output = m_PostProcessor[thread_id]->Process(&input, (*out)->spreadSize);
        (*out)->rect.width = output.width;
        (*out)->rect.height = output.height;
        *buffer = output.buffer;
    }
    else
    {
        // init bounds
        (*out)->bounds = GlyphBounds
        {
            static_cast<float>(slot->bitmap_left),
            static_cast<float>(slot->bitmap_top),
            static_cast<float>(bitmap.width),
            static_cast<float>(bitmap.rows)
        };

        // Step 6: Pack Glyph
        (*out)->rect.width = srcBitmap->width;
        (*out)->rect.height = srcBitmap->rows;
        if (bytesPP > 1)
        {
            if (!g_bitmap8bppInit)
            {
                FT_Bitmap_New(&g_bitmap8bpp);
                g_bitmap8bppInit = true;
            }
            FT_Bitmap_Convert(s_FTContext[thread_id].Library(), &bitmap, &g_bitmap8bpp, 1);
            srcBitmap = &g_bitmap8bpp;
            /*
            if (bitmap.pixel_mode != FT_PIXEL_MODE_GRAY)
            {
                if (!g_bitmap8bppInit)
                {
                    FT_Bitmap_New(&g_bitmap8bpp);
                    g_bitmap8bppInit = true;
                }
                FT_Bitmap_Convert(g_ftLib, &bitmap, &g_bitmap8bpp, 4);
                srcBitmap = &g_bitmap8bpp;
                if (srcBitmap->num_grays != 256)
                {
                    float factor = 1.0f / (srcBitmap->num_grays - 1) * 255;
                    for (int i = 0; i < srcBitmap->pitch * srcBitmap->rows; i++)
                        srcBitmap->buffer[i] *= factor;
                }
                srcBitmap = &bitmap;
            }
            */
        }

        *buffer = srcBitmap->buffer;
    }

    //if (!TryPackGlyph(c, roundedSize, srcBitmap->buffer, *out))
    //{
        // output error!
    //    return false;
    //}

    //out->rect.x += slot->bitmap_left;
    //out->rect.y += (slot->bitmap_top - srcBitmap->rows);

//printf("%d pack succeed!\n", c);

    return true;
}

bool UniFontFreeType::RenderGlyph(GlyphRenderData&& glyphRenderData, uint32 thread_id)
{
    if (glyphRenderData.renderMode == RenderMode::GSDF)
    {
        return m_SdfRenderer.RenderGlyph(glyphRenderData);
    }

    return m_FTRenderer.RenderGlyph(glyphRenderData);
}

void UniFontFreeType::OnInitialized()
{
    FT_Set_Pixel_Sizes(m_FTFace[0], m_BaseFontSize, 0);
    auto ratio = (float)m_FTFace[0]->size->metrics.y_ppem / (float)m_FTFace[0]->units_per_EM;
    m_BaseAscender = (float)m_FTFace[0]->ascender * ratio;
    m_BaseDescender = (float)m_FTFace[0]->descender * ratio;

    m_FTRenderer.Initialize(s_FTContext[0].Library(), m_FTFace[0]);
    m_SdfRenderer.Initialize(&m_OutlineFuncs);
}

const std::string& UniFontFreeType::GetFamilyName() const
{
    if (!m_FamilyNameCached)
    {
        if (m_FTFace[0] && m_FTFace[0]->family_name)
        {
            m_CachedFamilyName = m_FTFace[0]->family_name;
        }
        else
        {
            m_CachedFamilyName.clear();
        }
        m_FamilyNameCached = true;
    }
    return m_CachedFamilyName;
}

FontStyle UniFontFreeType::GetFontStyle() const
{
    if (!m_FTFace[0] || !m_FTFace[0]->style_name)
    {
        return FontStyle::Regular;
    }

    std::string styleName(m_FTFace[0]->style_name);

    // Convert to lowercase for comparison
    std::transform(styleName.begin(), styleName.end(), styleName.begin(), ::tolower);

    // Check for bold and italic combinations
    bool isBold = (styleName.find("bold") != std::string::npos) ||
                  (styleName.find("black") != std::string::npos) ||
                  (styleName.find("heavy") != std::string::npos);

    bool isItalic = (styleName.find("italic") != std::string::npos) ||
                    (styleName.find("oblique") != std::string::npos) ||
                    (styleName.find("slant") != std::string::npos);

    if (isBold && isItalic)
        return FontStyle::BoldItalic;
    else if (isBold)
        return FontStyle::Bold;
    else if (isItalic)
        return FontStyle::Italic;
    else
        return FontStyle::Regular;
}

#if USE_HARFBUZZ
bool UniFontFreeType::GetFontData(const unsigned char** outData, unsigned long* outSize) const
{
    if (outData && outSize && m_FontData && m_FontDataSize > 0)
    {
        *outData = m_FontData;
        *outSize = m_FontDataSize;
        return true;
    }
    return false;
}

void* UniFontFreeType::GetFontFace() const
{
    // Return the first FreeType face (thread 0)
    // HarfBuzz typically uses a single face for shaping
    return m_FTFace[0];
}
#endif // USE_HARFBUZZ

#endif // USE_FREETYPE