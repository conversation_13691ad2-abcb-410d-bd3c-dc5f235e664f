#ifndef __UniTextGenerator_h__
#define __UniTextGenerator_h__

#include "Font/UniFont.h"

#include <memory>
#include <stack>
#include <unordered_map>
#include <optional>
#include <variant>
#include <array>

#include "UniTextProcessor.h"

NAMESPACE_BEGIN

// Forward declarations
struct ShapingResult;

/// <summary>
/// UniTextGenerator.h
/// 负责string->Unicode转换，负责调用Font接口，生成对应quad
/// *可能负责字符排列
/// *可能负责与C#交互
/// </summary>
class UniTextGenerator
{
    // Friend classes for unified text processing
    friend class SimpleTextProcessor;
    friend class HarfBuzzTextProcessor;
    friend class UnifiedTextProcessor;

public:
    /// @brief TODO: Dirty by type is not used yet
    enum DirtyType : unsigned short
    {
        None = 0,
        Positions   = 1 << 0,
        UVs         = 1 << 1,
        Colors      = 1 << 2,
        Vertices    = Positions | UVs | Colors,

        Alignment   = 1 << 3,

        LineSpacing = 1 << 5,
        CharSpacing = 1 << 6,

        All         = 0xffff
    };

    struct Vertex
    {
        Color color;
        Vector2f position;
        Vector4f uv;
    };

    struct MeshData
    {
        std::vector<Color> colors;
        std::vector<Vector2f> vertices;
        std::vector<Vector4f> uvs;

        void Reserve(int capacity)
        {
            colors.reserve(capacity);
            vertices.reserve(capacity);
            uvs.reserve(capacity);
        }

        void Clear()
        {
            colors.clear();
            vertices.clear();
            uvs.clear();
        }
    };

    /// <summary>
    /// Pen, Letter, Word, Line are all necessary for character layout.
    /// </summary>
    struct Letter
    {
        Vector2f advance{ 0.0f, 0.0f };
        int vertexIdx;
    };

    struct Word
    {
        int startIndex{ -1 };
        int endIndex{ -1 };
    };

    struct Line
    {
        int startIndex{ -1 };
        // endIndex = nextLine.startIndex - thisLine.startIndex;
        int endIndex{ -1 };
        float width{ 0.0f };
        float height{ 0.0f };
        float offset{ 0.0f };    // x offset : for alignment
        int maxFontSize{ 0 };
    };

    struct Pen
    {
        uint16 prevUnicode;
        int currentCharIdx;
        // x,y is the next character's bottom left corner.
        // That makes y the baseline height.
        float x;
        float y;
        float lineSpacing;
        float minX, minY, maxX, maxY;
        // start offset
        Vector2f offset;

        void Reset() {
            prevUnicode = 0;
            currentCharIdx = 0;
            x = 0.0f;
            y = 0.0f;
            lineSpacing = 0.0f;
            minX = minY = maxX = maxY = 0.0f;
            offset = Vector2f(0.0f, 0.0f);
        }
    };

public:
    UniTextGenerator();
    ~UniTextGenerator();

    void SetActive(bool active);
    bool IsActive() const noexcept { return m_IsActive; }

#pragma region feature
    DEFINE_BITS_PROPERTY(RichText, 0, m_Features, SetDirty())
    /// <summary>
    /// AutoSize is a bit different from Unity's implementation
    /// Only do autosizing when exceeds vertical bounds.
    /// </summary>
    DEFINE_BITS_PROPERTY(AutoSize, 1, m_Features, SetDirty())
    DEFINE_BITS_PROPERTY(Kerning,  2, m_Features, SetDirty())
    DEFINE_BITS_PROPERTY(RTL,      3, m_Features, SetDirty())
#pragma endregion

    void SetText(const char* text);
    void AppendText(const char* text);

    void SetFont(const char* fontName);
    void SetFont(UniFont* uniFont);

    void SetColor(const Color& color) { if (m_BaseColor == color) return; m_BaseColor = color; SetDirty(); }
    Color GetColor() const { return m_BaseColor; }

    void SetHorizontalOverflow(TextOverflow textOverflow) { if (m_HorizontalOverflow == textOverflow) return; m_HorizontalOverflow = textOverflow; SetDirty(); }
    TextOverflow GetHorizontalOverflow() const { return m_HorizontalOverflow; }
    void SetVerticalOverflow(TextOverflow textOverflow) { if (m_VerticalOverflow == textOverflow) return; m_VerticalOverflow = textOverflow; SetDirty(); }
    TextOverflow GetVerticalOverflow() const { return m_VerticalOverflow; }
    void SetExtents(float width, float height);
    inline const Vector2f& GetExtents() const { return m_Extents; }
    inline void GetExtents(Vector2f& extents) const { extents = m_Extents; }

    void SetFontSize(int16 fontSize);
    inline int16 GetFontSize() const { return m_BaseFontSize; }
    /// <summary>
    /// Auto-sizing support
    /// </summary>
    void SetAutoSizeRange(int16 minFontSize, int16 maxFontSize);
    std::tuple<int16, int16> GetAutoSizeRange() const { return std::make_tuple(m_AutoSizeMinFontSize, m_BaseFontSize); }

    void SetHorizontalAlignment(TextAlignment alignment);
    TextAlignment GetHorizontalAlignment() const { return m_HorizontalAlignment; }
    void SetVerticalAlignment(TextAlignment alignment);
    TextAlignment GetVerticalAlignment() const { return m_VerticalAlignment; }
    void SetPivot(Vector2f pivot);
    Vector2f GetPivot() const { return m_Pivot; }
    void SetStyle(/*TODO: Not yet implemented*/);
    void SetLineSpacing(float lineSpacing);
    float GetLineSpacing() const { return m_BaseLineSpacing; }
    void SetCharacterSpacing(float charSpacing);
    float GetCharacterSpacing() const { return m_BaseCharSpacing; }

    void SetStrokeSize(float strokeSize);
    float GetStrokeSize() const { return m_BaseStrokeSize; }

    UniFont* GetFont() const { return m_Font; }

    /**
     * @brief Set the font style for text rendering (when using font family)
     * @param style Font style to use
     */
    void SetFontStyle(FontStyle style);

    /**
     * @brief Get the current font style
     * @return Current font style
     */
    FontStyle GetFontStyle() const { return m_FontStyle; }

    inline const ITexture* GetFontAtlas(int32 index = 0) const
    {
        return m_Font ? m_Font->GetFontAtlas(m_BaseFontSize, index) : nullptr;
    }

    inline const IUniGlyphPacker* GetAtlasPacker(int index = 0) const
    {
        return m_Font ? m_Font->GetAtlasPacker(m_BaseFontSize, index) : nullptr;
    }

    // Public APIs for text processors
    const std::pmr::vector<uint16>& GetUnicodes() const noexcept { return m_Unicodes; }
    int GetBaseFontSize() const noexcept { return m_BaseFontSize; }

    // Direct access to core data structures
    Pen& GetPen() noexcept { return m_Pen; }
    const Pen& GetPen() const noexcept { return m_Pen; }

    std::pmr::vector<Letter>& GetLetters() noexcept { return m_Letters; }
    const std::pmr::vector<Letter>& GetLetters() const noexcept { return m_Letters; }

    std::pmr::vector<Word>& GetWords() noexcept { return m_Words; }
    const std::pmr::vector<Word>& GetWords() const noexcept { return m_Words; }

    std::pmr::vector<Line>& GetLines() noexcept { return m_Lines; }
    const std::pmr::vector<Line>& GetLines() const noexcept { return m_Lines; }

    MeshData& GetMeshData() noexcept { return m_MeshData; }
    const MeshData& GetMeshData() const noexcept { return m_MeshData; }

    // Helper APIs for common operations
    void InitializePenWithOffset() noexcept;
    void ResetPenHorizontally() noexcept;
    float GetKerning(uint16 first, uint16 second) const noexcept;

    void Rebuild();
    void Rebuild(bool enableAutoSizing);
#ifndef UNITY
    static void RebuildUniTexts();
#endif

#pragma region RichText Tag
    // Attribute value that can hold different types (using UniStringView for consistency and zero-copy performance)
    using AttributeValue = std::variant<int, float, UniStringView, Color, FontStyle>;
    // Map to store multiple attributes per tag (using UniStringView for consistency and zero-copy performance)
    using AttributeMap = std::unordered_map<UniStringView, AttributeValue>;

    struct RichTextTag
    {
        // Multiple attributes with zero-copy string_view
        AttributeMap attributes;

        int startIndex{ -1 };
        int endIndex{ -1 };

        // For tags like <quad ... />
        bool isSelfClosing{ false };

        // Parsing validity flag (combines TagParseResult functionality)
        bool isValid{ false };

        // Helper methods to get specific attribute types (using UniStringView for consistency)
        template<typename T>
        std::optional<T> GetAttribute(UniStringView name) const
        {
            auto it = attributes.find(name);
            if (it != attributes.end())
            {
                if (std::holds_alternative<T>(it->second))
                {
                    return std::get<T>(it->second);
                }
            }
            return std::nullopt;
        }

        // Set attribute with type safety
        template<typename T>
        void SetAttribute(UniStringView name, const T& value)
        {
            attributes[name] = value;
        }

    private:
        // Helper to get the "value" key as UniStringView
        static UniStringView GetValueKey()
        {
            static thread_local std::vector<uint16> valueKey = {
                static_cast<uint16>('v'), static_cast<uint16>('a'), static_cast<uint16>('l'),
                static_cast<uint16>('u'), static_cast<uint16>('e')
            };
            return UniStringView(valueKey.data(), valueKey.size());
        }

    public:
        // Unified value access - gets the main value for simple tags
        template<typename T>
        std::optional<T> GetValue() const
        {
            return GetAttribute<T>(GetValueKey());
        }

        // Set the main value for simple tags
        template<typename T>
        void SetValue(const T& value)
        {
            SetAttribute(GetValueKey(), value);
        }
    };

    using FormatStack = std::vector<RichTextTag>;

    struct PropertyModifier
    {
        bool isUnderline{false};
        bool isDeleteline{false};
        bool isInsideNobr{false};
        std::vector<FontStyle> style;
        std::vector<Color> color;
        std::vector<int> fontSize;

        void clear()
        {
            isUnderline = false;
            isDeleteline = false;
            isInsideNobr = false;
            style.clear();
            color.clear();
            fontSize.clear();
        }
    };
#pragma endregion

private:
    void PreProcessTexts(const uint16* unicodes, int length);
    void ApplyAlignment() noexcept;

    void SetDirty() noexcept;
    void SetDirty(DirtyType dirtyType, bool isDirty) noexcept;
    const bool IsDirty(DirtyType dirtyType) const noexcept
    {
        return m_DirtyFlags.HasBit(static_cast<uint16>(dirtyType));
    }

    void GetTextOffset(Vector2f& offset) noexcept;
    const bool ShouldBreakWord(const uint16 unicode) noexcept;

    void IterateRichTextTags(const int curIndex) noexcept;

    template<RichTextTagType tagType>
    void IterateRichTextTag(const int curIndex) noexcept;
    template<RichTextTagType tagType>
    void ApplyRichTextTag(const RichTextTag& tag) noexcept;
    template<RichTextTagType tagType>
    void RestoreRichTextTag(const RichTextTag& tag) noexcept;

    // Unified text processing method
    bool ProcessTextUnified() noexcept;

    bool InsertCharacter(uint16 unicode) noexcept;
    bool InsertSpace(uint16 unicode, int count = 1) noexcept;
    bool InsertNewLine() noexcept;

    // Post-processing methods for improved wrapping (Phase 2)
    void ProcessWrapping() noexcept;
    void WrapLine(int lineIndex) noexcept;
    void ProcessOverflow() noexcept;
    void ApplyOverflow(int lineIndex) noexcept;

    // Auto-sizing methods (Phase 2.5)
    bool ProcessAutoSizing() noexcept;
    bool CheckFontSizeFits(int16 testFontSize) noexcept;

    // Word-based wrapping helpers
    void BuildWordBoundaries() noexcept;
    int FindWordBoundaryBefore(int letterIndex) noexcept;

    void AddGlyphQuad(const Vector2f& bottomLeft, float scale, const Color& color, const GlyphInfo& glyph, const SizedPage* page) noexcept;
    void AddEmptyQuad(const Vector2f& bottomLeft, const Vector2f& size, const Color& color) noexcept;
    void AddLineQuad(const Vector2f& startPos, const Vector2f& endPos, float thickness, const Color& color) noexcept;
    void OffsetQuad(int startIndex, int endIndex, const Vector2f& offset) noexcept;

    void MarkTextsAsUnused();

    Line& CurrentLine() noexcept { return m_Lines[m_Lines.size() - 1]; }
    bool m_IsActive;
    bool m_IsDirty;

    Bits<uint8> m_Features;
    Bits<uint16> m_DirtyFlags;

    TextOverflow m_HorizontalOverflow;
    TextOverflow m_VerticalOverflow;
    TextAlignment m_HorizontalAlignment;
    TextAlignment m_VerticalAlignment;

    // Base Property
    int16 m_BaseFontSize;
    int16 m_LastFontSize;
    int16 m_AutoSizeMinFontSize;
    // AutoSizeMaxFontSize is irrlevant since we only shrink down
    // int16 m_AutoSizeMaxFontSize;
    Color m_BaseColor;
    float m_BaseStrokeSize;
    float m_BaseLineSpacing;
    float m_BaseCharSpacing;

    // Text Bounds: defined with Pivot(x, y), Extents(width, height)
    Vector2f m_Extents;
    Vector2f m_Pivot;

    UniFont* m_Font;
    UniFont* m_LastUsedFont;

    FontStyle m_FontStyle;

    std::unique_ptr<UnifiedTextProcessor> m_UnifiedProcessor;

    LocalArenaMemoryResource<32> m_LocalMemResForString;
    LocalArenaMemoryResource<32> m_LocalMemResForVector;
    // @TODO:
    // use std::string with custom allocator to reduce memory allocation
    UniString m_Text;
    // @TODO:
    // use std::vector with custom allocator to reduce memory allocation
    std::pmr::vector<uint16> m_Unicodes;

    std::array<FormatStack, static_cast<size_t>(RichTextTagType::Max)> m_FormatStack{};

    MeshData m_MeshData{};
    // Property Modifier(for rich text)
    PropertyModifier m_PropertyModifier{};

    /// @brief Pen, Letter, Word, Line are all necessary for character layout.
    Pen m_Pen{};
    std::pmr::vector<Letter> m_Letters{};
    std::pmr::vector<Word> m_Words{};
    std::pmr::vector<Line> m_Lines{};
};

NAMESPACE_END

#endif // __UniTextGenerator_h__