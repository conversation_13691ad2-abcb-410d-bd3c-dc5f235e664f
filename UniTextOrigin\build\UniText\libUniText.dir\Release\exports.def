EXPORTS 
	??_R0?AU?$LocalArenaMemoryResource@$0CA@@UniText@@@8 	 DATA
	??_R0?AU?$LocalArenaMemoryResource@$0IA@@UniText@@@8 	 DATA
	??_R0?AUunsynchronized_pool_resource@pmr@std@@@8 	 DATA
	??_R0?AV?$MaxRectsGlyphPacker@$0A@@UniText@@@8 	 DATA
	??_R0?AV?$_Func_base@XPEAVUniFont@UniText@@@std@@@8 	 DATA
	??_R0?AV?$_Ref_count_obj2@U_Dir_enum_impl@filesystem@std@@@std@@@8 	 DATA
	??_R0?AV?$_Ref_count_obj2@U_Recursive_dir_enum_impl@filesystem@std@@@std@@@8 	 DATA
	??_R0?AV?$array@M$08@std@@@8 	 DATA
	??_R0?AVAndroidFontFallbackProvider@UniText@@@8 	 DATA
	??_R0?AVAppleFontFallbackProvider@UniText@@@8 	 DATA
	??_R0?AVDefaultFontFallbackProvider@UniText@@@8 	 DATA
	??_R0?AVGridGlyphPacker@UniText@@@8 	 DATA
	??_R0?AVIFontFallbackProvider@UniText@@@8 	 DATA
	??_R0?AVITextProcessor@UniText@@@8 	 DATA
	??_R0?AVITexture@UniText@@@8 	 DATA
	??_R0?AVIUniFontImpl@UniText@@@8 	 DATA
	??_R0?AVIUniGlyphPacker@UniText@@@8 	 DATA
	??_R0?AVIUniPostProcessor@UniText@@@8 	 DATA
	??_R0?AVSDFGenerator@UniText@@@8 	 DATA
	??_R0?AVShelfGlyphPacker@UniText@@@8 	 DATA
	??_R0?AVSimpleTextProcessor@UniText@@@8 	 DATA
	??_R0?AVUniCustomFont@UniText@@@8 	 DATA
	??_R0?AVUniFont@UniText@@@8 	 DATA
	??_R0?AVUniFontFreeType@UniText@@@8 	 DATA
	??_R0?AVUniObject@UniText@@@8 	 DATA
	??_R0?AVUniTextureNative@UniText@@@8 	 DATA
	??_R0?AVVSDFGenerator@UniText@@@8 	 DATA
	??_R0?AVWindowsFontFallbackProvider@UniText@@@8 	 DATA
	??_R0?AVWriteToFileGenerator@UniText@@@8 	 DATA
	??_R0?AV_Generic_error_category@std@@@8 	 DATA
	??_R0?AV_Identity_equal_resource@pmr@std@@@8 	 DATA
	??_R0?AV_Ref_count_base@std@@@8 	 DATA
	??_R0?AV_System_error@std@@@8 	 DATA
	??_R0?AV_System_error_category@std@@@8 	 DATA
	??_R0?AVbad_alloc@std@@@8 	 DATA
	??_R0?AVbad_array_new_length@std@@@8 	 DATA
	??_R0?AVbad_optional_access@std@@@8 	 DATA
	??_R0?AVbad_variant_access@std@@@8 	 DATA
	??_R0?AVerror_category@std@@@8 	 DATA
	??_R0?AVexception@std@@@8 	 DATA
	??_R0?AVfilesystem_error@filesystem@std@@@8 	 DATA
	??_R0?AVmemory_resource@pmr@std@@@8 	 DATA
	??_R0?AVruntime_error@std@@@8 	 DATA
	??_R0?AVsystem_error@std@@@8 	 DATA
	?WriteToPngFile@WriteToFileGenerator@UniText@@2P6AXPEBDAEBV?$vector@EV?$allocator@E@std@@@std@@HHH@ZEA 	 DATA
	?_OptionsStorage@?1??__local_stdio_printf_options@@9@4_KA 	 DATA
	?_Static@?1???$_Immortalize_memcpy_image@V_Generic_error_category@std@@@std@@YAAEBV_Generic_error_category@1@XZ@4U?$_Constexpr_immortalize_impl@V_Generic_error_category@std@@@1@A 	 DATA
	?_Static@?1???$_Immortalize_memcpy_image@V_System_error_category@std@@@std@@YAAEBV_System_error_category@1@XZ@4U?$_Constexpr_immortalize_impl@V_System_error_category@std@@@1@A 	 DATA
	?_iterator@@3V?$vector@HV?$allocator@H@std@@@std@@A 	 DATA
	?_pool@?$LocalArenaMemoryResource@$0CA@@UniText@@0Uunsynchronized_pool_resource@pmr@std@@A 	 DATA
	?_pool@?$LocalArenaMemoryResource@$0EAA@@UniText@@0Uunsynchronized_pool_resource@pmr@std@@A 	 DATA
	?_pool@?$LocalArenaMemoryResource@$0IA@@UniText@@0Uunsynchronized_pool_resource@pmr@std@@A 	 DATA
	?black@Color@UniText@@2U12@B 	 DATA
	?blue@Color@UniText@@2U12@B 	 DATA
	?gLogger@UniText@@3P6AXHPEBDZZEA 	 DATA
	?gNewTextureCallback@UniText@@3P6APEAXPEAX@ZEA 	 DATA
	?green@Color@UniText@@2U12@B 	 DATA
	?kFS_Fill_glsl@@3PEBDEB 	 DATA
	?kFS_Fill_hlsl@@3PEBDEB 	 DATA
	?kFS_Line_glsl@@3PEBDEB 	 DATA
	?kFS_Line_hlsl@@3PEBDEB 	 DATA
	?kVS_Fill_glsl@@3PEBDEB 	 DATA
	?kVS_Fill_hlsl@@3PEBDEB 	 DATA
	?kVS_Line_glsl@@3PEBDEB 	 DATA
	?kVS_Line_hlsl@@3PEBDEB 	 DATA
	?red@Color@UniText@@2U12@B 	 DATA
	?s_MemPool@UTF16String@UniText@@2V?$SharedMemPool@G@2@A 	 DATA
	?white@Color@UniText@@2U12@B 	 DATA
	?zero@?$Vector2@M@UniText@@2U12@A 	 DATA
	?<lambda_invoker_cdecl>@<lambda_386a743bebce765660ea17f5aaaeac7e>@@CAHPEBUFT_Vector_@@0PEAX@Z
	?<lambda_invoker_cdecl>@<lambda_821f3da23b129b0afca247907edfe748>@@CAHPEBUFT_Vector_@@PEAX@Z
	?<lambda_invoker_cdecl>@<lambda_dc688a12290ca8f9f3692d6885975fee>@@CAHPEBUFT_Vector_@@00PEAX@Z
	?<lambda_invoker_cdecl>@<lambda_f06a024fefff80b792675ae8f6a3db0f>@@CAHPEBUFT_Vector_@@PEAX@Z
	??$?0V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$0A@@path@filesystem@std@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@W4format@012@@Z
	??$?8$$A6AXAEBU?$Rect@G@UniText@@_N@Z@std@@YA_NAEBV?$function@$$A6AXAEBU?$Rect@G@UniText@@_N@Z@0@$$T@Z
	??$?8DU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@QEBD@Z
	??$?9U_Dir_enum_impl@filesystem@std@@U012@@std@@YA_NAEBV?$shared_ptr@U_Dir_enum_impl@filesystem@std@@@0@0@Z
	??$?9U_Recursive_dir_enum_impl@filesystem@std@@U012@@std@@YA_NAEBV?$shared_ptr@U_Recursive_dir_enum_impl@filesystem@std@@@0@0@Z
	??$?9VIUniGlyphPacker@UniText@@U?$default_delete@VIUniGlyphPacker@UniText@@@std@@@std@@YA_NAEBV?$unique_ptr@VIUniGlyphPacker@UniText@@U?$default_delete@VIUniGlyphPacker@UniText@@@std@@@0@$$T@Z
	??$?9VIUniPostProcessor@UniText@@U?$default_delete@VIUniPostProcessor@UniText@@@std@@@std@@YA_NAEBV?$unique_ptr@VIUniPostProcessor@UniText@@U?$default_delete@VIUniPostProcessor@UniText@@@std@@@0@$$T@Z
	??$?_0V?$basic_string_view@_WU?$char_traits@_W@std@@@std@@$0A@@path@filesystem@std@@QEAAAEAV012@AEBV?$basic_string_view@_WU?$char_traits@_W@std@@@2@@Z
	??$ApplyRichTextTag@$00@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$ApplyRichTextTag@$01@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$ApplyRichTextTag@$02@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$ApplyRichTextTag@$03@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$ApplyRichTextTag@$04@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$ApplyRichTextTag@$05@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$ApplyRichTextTag@$06@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$ApplyRichTextTag@$07@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$ApplyRichTextTag@$0A@@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$GetFont@$00@UniFontCache@UniText@@QEAAPEAVUniFont@1@PEBD@Z
	??$GetFont@$0A@@UniFontCache@UniText@@QEAAPEAVUniFont@1@PEBD@Z
	??$IterateRichTextTag@$00@UniTextGenerator@UniText@@AEAAXH@Z
	??$IterateRichTextTag@$01@UniTextGenerator@UniText@@AEAAXH@Z
	??$IterateRichTextTag@$02@UniTextGenerator@UniText@@AEAAXH@Z
	??$IterateRichTextTag@$03@UniTextGenerator@UniText@@AEAAXH@Z
	??$IterateRichTextTag@$04@UniTextGenerator@UniText@@AEAAXH@Z
	??$IterateRichTextTag@$05@UniTextGenerator@UniText@@AEAAXH@Z
	??$IterateRichTextTag@$06@UniTextGenerator@UniText@@AEAAXH@Z
	??$IterateRichTextTag@$07@UniTextGenerator@UniText@@AEAAXH@Z
	??$IterateRichTextTag@$0A@@UniTextGenerator@UniText@@AEAAXH@Z
	??$LoadGlyph@$00@UniFont@UniText@@QEAA_NAEBUGlyphLoadParam@1@PEAPEAUGlyphInfo@1@@Z
	??$LoadGlyph@$0A@@UniFont@UniText@@QEAA_NAEBUGlyphLoadParam@1@PEAPEAUGlyphInfo@1@@Z
	??$PutOnLayer@$00@ShelfGlyphPacker@UniText@@AEAA?B_NAEAULayer@01@PEAU?$Rect@G@1@GG@Z
	??$RestoreRichTextTag@$00@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$RestoreRichTextTag@$01@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$RestoreRichTextTag@$02@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$RestoreRichTextTag@$03@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$RestoreRichTextTag@$04@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$RestoreRichTextTag@$05@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$RestoreRichTextTag@$06@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$RestoreRichTextTag@$07@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$RestoreRichTextTag@$0A@@UniTextGenerator@UniText@@AEAAXAEBURichTextTag@01@@Z
	??$SetValue@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@std@@@RichTextTag@UniTextGenerator@UniText@@QEAAXAEBV?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@std@@@Z
	??$_Allocate@$0BA@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
	??$_Assign_counted_range@PEAH@?$vector@HV?$allocator@H@std@@@std@@AEAAXPEAH_K@Z
	??$_Assign_counted_range@PEAUSdfVertex@UniText@@@?$vector@USdfVertex@UniText@@V?$allocator@USdfVertex@UniText@@@std@@@std@@AEAAXPEAUSdfVertex@UniText@@_K@Z
	??$_Assign_counted_range@PEBE@?$vector@EV?$allocator@E@std@@@std@@AEAAXPEBE_K@Z
	??$_Assign_counted_range@PEBW4FontStyle@UniText@@@?$vector@W4FontStyle@UniText@@V?$allocator@W4FontStyle@UniText@@@std@@@std@@AEAAXPEBW4FontStyle@UniText@@_K@Z
	??$_Assign_counted_range@PEBW4ScriptType@UniText@@@?$vector@W4ScriptType@UniText@@V?$allocator@W4ScriptType@UniText@@@std@@@std@@AEAAXPEBW4ScriptType@UniText@@_K@Z
	??$_Atomic_address_as@HU?$_Atomic_padded@H@std@@@std@@YAPEDHAEBU?$_Atomic_padded@H@0@@Z
	??$_Atomic_address_as@JU?$_Atomic_padded@H@std@@@std@@YAPECJAEAU?$_Atomic_padded@H@0@@Z
	??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z
	??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
	??$_Convert_Source_to_wide@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U_Normal_conversion@filesystem@2@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@U_Normal_conversion@01@@Z
	??$_Convert_Source_to_wide@V?$basic_string_view@_WU?$char_traits@_W@std@@@std@@U_Normal_conversion@filesystem@2@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@AEBV?$basic_string_view@_WU?$char_traits@_W@std@@@1@U_Normal_conversion@01@@Z
	??$_Convert_stringoid_to_wide@U_Normal_conversion@filesystem@std@@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@V?$basic_string_view@DU?$char_traits@D@std@@@1@U_Normal_conversion@01@@Z
	??$_Convert_stringoid_to_wide@U_Normal_conversion@filesystem@std@@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@V?$basic_string_view@_WU?$char_traits@_W@std@@@1@U_Normal_conversion@01@@Z
	??$_Convert_wide_to@U?$char_traits@D@std@@V?$allocator@D@2@D@filesystem@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@V?$basic_string_view@_WU?$char_traits@_W@std@@@1@AEBV?$allocator@D@1@@Z
	??$_Convert_wide_to_narrow@U?$char_traits@D@std@@V?$allocator@D@2@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@W4__std_code_page@@V?$basic_string_view@_WU?$char_traits@_W@std@@@0@AEBV?$allocator@D@0@@Z
	??$_Convert_wide_to_narrow_replace_chars@U?$char_traits@D@std@@V?$allocator@D@2@@filesystem@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@W4__std_code_page@@V?$basic_string_view@_WU?$char_traits@_W@std@@@1@AEBV?$allocator@D@1@@Z
	??$_Copy_backward_memmove@PEAULine@UniTextGenerator@UniText@@PEAU123@@std@@YAPEAULine@UniTextGenerator@UniText@@PEAU123@00@Z
	??$_Copy_backward_memmove@PEAURange@ShelfGlyphPacker@UniText@@PEAU123@@std@@YAPEAURange@ShelfGlyphPacker@UniText@@PEAU123@00@Z
	??$_Copy_memmove@PEAEPEAE@std@@YAPEAEPEAE00@Z
	??$_Copy_memmove@PEAHPEAH@std@@YAPEAHPEAH00@Z
	??$_Copy_memmove@PEAIPEAI@std@@YAPEAIPEAI00@Z
	??$_Copy_memmove@PEAMPEAM@std@@YAPEAMPEAM00@Z
	??$_Copy_memmove@PEAPEAHPEAPEAH@std@@YAPEAPEAHPEAPEAH00@Z
	??$_Copy_memmove@PEAPEAUSdf_RenderData@UniText@@PEAPEAU12@@std@@YAPEAPEAUSdf_RenderData@UniText@@PEAPEAU12@00@Z
	??$_Copy_memmove@PEAPEAVUniFont@UniText@@PEAPEAV12@@std@@YAPEAPEAVUniFont@UniText@@PEAPEAV12@00@Z
	??$_Copy_memmove@PEAU?$IndexedQuadTree@$06@UniText@@PEAU12@@std@@YAPEAU?$IndexedQuadTree@$06@UniText@@PEAU12@00@Z
	??$_Copy_memmove@PEAU?$Rect@G@UniText@@PEAU12@@std@@YAPEAU?$Rect@G@UniText@@PEAU12@00@Z
	??$_Copy_memmove@PEAU?$Vector2@G@UniText@@PEAU12@@std@@YAPEAU?$Vector2@G@UniText@@PEAU12@00@Z
	??$_Copy_memmove@PEAU?$Vector2@M@UniText@@PEAU12@@std@@YAPEAU?$Vector2@M@UniText@@PEAU12@00@Z
	??$_Copy_memmove@PEAU?$Vector4@M@UniText@@PEAU12@@std@@YAPEAU?$Vector4@M@UniText@@PEAU12@00@Z
	??$_Copy_memmove@PEAURange@ShelfGlyphPacker@UniText@@PEAU123@@std@@YAPEAURange@ShelfGlyphPacker@UniText@@PEAU123@00@Z
	??$_Copy_memmove@PEAUSdfVertex@UniText@@PEAU12@@std@@YAPEAUSdfVertex@UniText@@PEAU12@00@Z
	??$_Copy_memmove@PEAW4FontStyle@UniText@@PEAW412@@std@@YAPEAW4FontStyle@UniText@@PEAW412@00@Z
	??$_Copy_memmove@PEAW4ScriptType@UniText@@PEAW412@@std@@YAPEAW4ScriptType@UniText@@PEAW412@00@Z
	??$_Copy_memmove@PEBEPEAE@std@@YAPEAEPEBE0PEAE@Z
	??$_Copy_memmove@PEBW4ScriptType@UniText@@PEAW412@@std@@YAPEAW4ScriptType@UniText@@PEBW412@0PEAW412@@Z
	??$_Copy_memmove_n@PEAHPEAH@std@@YAPEAHPEAH_K0@Z
	??$_Copy_memmove_n@PEAUSdfVertex@UniText@@PEAU12@@std@@YAPEAUSdfVertex@UniText@@PEAU12@_K0@Z
	??$_Copy_memmove_n@PEBEPEAE@std@@YAPEAEPEBE_KPEAE@Z
	??$_Copy_memmove_n@PEBW4FontStyle@UniText@@PEAW412@@std@@YAPEAW4FontStyle@UniText@@PEBW412@_KPEAW412@@Z
	??$_Copy_memmove_n@PEBW4ScriptType@UniText@@PEAW412@@std@@YAPEAW4ScriptType@UniText@@PEBW412@_KPEAW412@@Z
	??$_Copy_memmove_tail@PEAE@std@@YAPEAEQEBDQEAE_K2@Z
	??$_Copy_memmove_tail@PEAH@std@@YAPEAHQEBDQEAH_K2@Z
	??$_Copy_memmove_tail@PEAI@std@@YAPEAIQEBDQEAI_K2@Z
	??$_Copy_memmove_tail@PEAM@std@@YAPEAMQEBDQEAM_K2@Z
	??$_Copy_memmove_tail@PEAPEAH@std@@YAPEAPEAHQEBDQEAPEAH_K2@Z
	??$_Copy_memmove_tail@PEAPEAUSdf_RenderData@UniText@@@std@@YAPEAPEAUSdf_RenderData@UniText@@QEBDQEAPEAU12@_K2@Z
	??$_Copy_memmove_tail@PEAPEAVUniFont@UniText@@@std@@YAPEAPEAVUniFont@UniText@@QEBDQEAPEAV12@_K2@Z
	??$_Copy_memmove_tail@PEAU?$IndexedQuadTree@$06@UniText@@@std@@YAPEAU?$IndexedQuadTree@$06@UniText@@QEBDQEAU12@_K2@Z
	??$_Copy_memmove_tail@PEAU?$Rect@G@UniText@@@std@@YAPEAU?$Rect@G@UniText@@QEBDQEAU12@_K2@Z
	??$_Copy_memmove_tail@PEAU?$Vector2@G@UniText@@@std@@YAPEAU?$Vector2@G@UniText@@QEBDQEAU12@_K2@Z
	??$_Copy_memmove_tail@PEAU?$Vector2@M@UniText@@@std@@YAPEAU?$Vector2@M@UniText@@QEBDQEAU12@_K2@Z
	??$_Copy_memmove_tail@PEAU?$Vector4@M@UniText@@@std@@YAPEAU?$Vector4@M@UniText@@QEBDQEAU12@_K2@Z
	??$_Copy_memmove_tail@PEAURange@ShelfGlyphPacker@UniText@@@std@@YAPEAURange@ShelfGlyphPacker@UniText@@QEBDQEAU123@_K2@Z
	??$_Copy_memmove_tail@PEAUSdfVertex@UniText@@@std@@YAPEAUSdfVertex@UniText@@QEBDQEAU12@_K2@Z
	??$_Copy_memmove_tail@PEAW4FontStyle@UniText@@@std@@YAPEAW4FontStyle@UniText@@QEBDQEAW412@_K2@Z
	??$_Copy_memmove_tail@PEAW4ScriptType@UniText@@@std@@YAPEAW4ScriptType@UniText@@QEBDQEAW412@_K2@Z
	??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@1@PEAU21@0@Z
	??$_Destroy_range@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@YAXPEAUFontFallbackInfo@UniText@@QEAU12@AEAV?$allocator@UFontFallbackInfo@UniText@@@0@@Z
	??$_Destroy_range@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@YAXPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@QEAV10@AEAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@0@@Z
	??$_Destroy_range@V?$polymorphic_allocator@ULayer@ShelfGlyphPacker@UniText@@@pmr@std@@@std@@YAXPEAULayer@ShelfGlyphPacker@UniText@@QEAU123@AEAV?$polymorphic_allocator@ULayer@ShelfGlyphPacker@UniText@@@pmr@0@@Z
	??$_Destroy_range@V?$polymorphic_allocator@USizedPage@UniText@@@pmr@std@@@std@@YAXPEAUSizedPage@UniText@@QEAU12@AEAV?$polymorphic_allocator@USizedPage@UniText@@@pmr@0@@Z
	??$_Emplace@UGlyphLookUpKey@UniText@@UGlyphInfo@2@@?$_Tree@V?$_Tmap_traits@UGlyphLookUpKey@UniText@@UGlyphInfo@2@U?$less@UGlyphLookUpKey@UniText@@@std@@V?$polymorphic_allocator@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@@pmr@5@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@_N@1@$$QEAUGlyphLookUpKey@UniText@@$$QEAUGlyphInfo@4@@Z
	??$_Emplace_one_at_back@AEBH@?$vector@HV?$allocator@H@std@@@std@@AEAAAEAHAEBH@Z
	??$_Emplace_one_at_back@AEBUColor@UniText@@@?$vector@UColor@UniText@@V?$allocator@UColor@UniText@@@std@@@std@@AEAAAEAUColor@UniText@@AEBU23@@Z
	??$_Emplace_reallocate@AEAE@?$vector@U_Pool@unsynchronized_pool_resource@pmr@std@@V?$polymorphic_allocator@U_Pool@unsynchronized_pool_resource@pmr@std@@@34@@std@@AEAAPEAU_Pool@unsynchronized_pool_resource@pmr@1@QEAU2341@AEAE@Z
	??$_Emplace_reallocate@AEAH@?$vector@HV?$allocator@H@std@@@std@@AEAAPEAHQEAHAEAH@Z
	??$_Emplace_reallocate@AEAULineDecoration@UniTextGenerator@UniText@@@?$vector@ULineDecoration@UniTextGenerator@UniText@@V?$polymorphic_allocator@ULineDecoration@UniTextGenerator@UniText@@@pmr@std@@@std@@AEAAPEAULineDecoration@UniTextGenerator@UniText@@QEAU234@AEAU234@@Z
	??$_Emplace_reallocate@AEAUWord@UniTextGenerator@UniText@@@?$vector@UWord@UniTextGenerator@UniText@@V?$polymorphic_allocator@UWord@UniTextGenerator@UniText@@@pmr@std@@@std@@AEAAPEAUWord@UniTextGenerator@UniText@@QEAU234@AEAU234@@Z
	??$_Emplace_reallocate@AEBG@?$vector@GV?$polymorphic_allocator@G@pmr@std@@@std@@AEAAPEAGQEAGAEBG@Z
	??$_Emplace_reallocate@AEBQEAVUniFont@UniText@@@?$vector@PEAVUniFont@UniText@@V?$allocator@PEAVUniFont@UniText@@@std@@@std@@AEAAPEAPEAVUniFont@UniText@@QEAPEAV23@AEBQEAV23@@Z
	??$_Emplace_reallocate@AEBU?$Rect@G@UniText@@@?$vector@U?$Rect@G@UniText@@V?$allocator@U?$Rect@G@UniText@@@std@@@std@@AEAAPEAU?$Rect@G@UniText@@QEAU23@AEBU23@@Z
	??$_Emplace_reallocate@AEBU?$Vector2@M@UniText@@@?$vector@U?$Vector2@M@UniText@@V?$allocator@U?$Vector2@M@UniText@@@std@@@std@@AEAAPEAU?$Vector2@M@UniText@@QEAU23@AEBU23@@Z
	??$_Emplace_reallocate@AEBU?$Vector4@M@UniText@@@?$vector@U?$Vector4@M@UniText@@V?$allocator@U?$Vector4@M@UniText@@@std@@@std@@AEAAPEAU?$Vector4@M@UniText@@QEAU23@AEBU23@@Z
	??$_Emplace_reallocate@AEBUFontFallbackInfo@UniText@@@?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@AEAAPEAUFontFallbackInfo@UniText@@QEAU23@AEBU23@@Z
	??$_Emplace_reallocate@AEBULine@UniTextGenerator@UniText@@@?$vector@ULine@UniTextGenerator@UniText@@V?$polymorphic_allocator@ULine@UniTextGenerator@UniText@@@pmr@std@@@std@@AEAAPEAULine@UniTextGenerator@UniText@@QEAU234@AEBU234@@Z
	??$_Emplace_reallocate@AEBUSdfVertex@UniText@@@?$vector@USdfVertex@UniText@@V?$allocator@USdfVertex@UniText@@@std@@@std@@AEAAPEAUSdfVertex@UniText@@QEAU23@AEBU23@@Z
	??$_Emplace_reallocate@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@QEAV21@AEBV21@@Z
	??$_Emplace_reallocate@AEBW4FontStyle@UniText@@@?$vector@W4FontStyle@UniText@@V?$allocator@W4FontStyle@UniText@@@std@@@std@@AEAAPEAW4FontStyle@UniText@@QEAW423@AEBW423@@Z
	??$_Emplace_reallocate@H@?$vector@HV?$allocator@H@std@@@std@@AEAAPEAHQEAH$$QEAH@Z
	??$_Emplace_reallocate@U?$IndexedQuadTree@$06@UniText@@@?$vector@U?$IndexedQuadTree@$06@UniText@@V?$allocator@U?$IndexedQuadTree@$06@UniText@@@std@@@std@@AEAAPEAU?$IndexedQuadTree@$06@UniText@@QEAU23@$$QEAU23@@Z
	??$_Emplace_reallocate@U?$Vector2@G@UniText@@@?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@AEAAPEAU?$Vector2@G@UniText@@QEAU23@$$QEAU23@@Z
	??$_Emplace_reallocate@U?$Vector4@M@UniText@@@?$vector@U?$Vector4@M@UniText@@V?$allocator@U?$Vector4@M@UniText@@@std@@@std@@AEAAPEAU?$Vector4@M@UniText@@QEAU23@$$QEAU23@@Z
	??$_Emplace_reallocate@UGlyphLookUpTable@UniText@@@?$vector@UGlyphLookUpTable@UniText@@V?$allocator@UGlyphLookUpTable@UniText@@@std@@@std@@AEAAPEAUGlyphLookUpTable@UniText@@QEAU23@$$QEAU23@@Z
	??$_Emplace_reallocate@ULayer@ShelfGlyphPacker@UniText@@@?$vector@ULayer@ShelfGlyphPacker@UniText@@V?$polymorphic_allocator@ULayer@ShelfGlyphPacker@UniText@@@pmr@std@@@std@@AEAAPEAULayer@ShelfGlyphPacker@UniText@@QEAU234@$$QEAU234@@Z
	??$_Emplace_reallocate@ULetter@UniTextGenerator@UniText@@@?$vector@ULetter@UniTextGenerator@UniText@@V?$polymorphic_allocator@ULetter@UniTextGenerator@UniText@@@pmr@std@@@std@@AEAAPEAULetter@UniTextGenerator@UniText@@QEAU234@$$QEAU234@@Z
	??$_Emplace_reallocate@ULine@UniTextGenerator@UniText@@@?$vector@ULine@UniTextGenerator@UniText@@V?$polymorphic_allocator@ULine@UniTextGenerator@UniText@@@pmr@std@@@std@@AEAAPEAULine@UniTextGenerator@UniText@@QEAU234@$$QEAU234@@Z
	??$_Emplace_reallocate@URange@ShelfGlyphPacker@UniText@@@?$vector@URange@ShelfGlyphPacker@UniText@@V?$polymorphic_allocator@URange@ShelfGlyphPacker@UniText@@@pmr@std@@@std@@AEAAPEAURange@ShelfGlyphPacker@UniText@@QEAU234@$$QEAU234@@Z
	??$_Emplace_reallocate@URichTextTag@UniTextGenerator@UniText@@@?$vector@URichTextTag@UniTextGenerator@UniText@@V?$allocator@URichTextTag@UniTextGenerator@UniText@@@std@@@std@@AEAAPEAURichTextTag@UniTextGenerator@UniText@@QEAU234@$$QEAU234@@Z
	??$_Emplace_reallocate@USdfVertex@UniText@@@?$vector@USdfVertex@UniText@@V?$allocator@USdfVertex@UniText@@@std@@@std@@AEAAPEAUSdfVertex@UniText@@QEAU23@$$QEAU23@@Z
	??$_Emplace_reallocate@USizedPage@UniText@@@?$vector@USizedPage@UniText@@V?$polymorphic_allocator@USizedPage@UniText@@@pmr@std@@@std@@AEAAPEAUSizedPage@UniText@@QEAU23@$$QEAU23@@Z
	??$_Emplace_reallocate@U_Find_file_handle@filesystem@std@@@?$vector@U_Find_file_handle@filesystem@std@@V?$allocator@U_Find_file_handle@filesystem@std@@@3@@std@@AEAAPEAU_Find_file_handle@filesystem@1@QEAU231@$$QEAU231@@Z
	??$_Emplace_reallocate@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@QEAV21@$$QEAV21@@Z
	??$_Emplace_reallocate@V?$unique_ptr@VUniTextureNative@UniText@@U?$default_delete@VUniTextureNative@UniText@@@std@@@std@@@?$vector@V?$unique_ptr@VITexture@UniText@@U?$default_delete@VITexture@UniText@@@std@@@std@@V?$allocator@V?$unique_ptr@VITexture@UniText@@U?$default_delete@VITexture@UniText@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VITexture@UniText@@U?$default_delete@VITexture@UniText@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VUniTextureNative@UniText@@U?$default_delete@VUniTextureNative@UniText@@@std@@@1@@Z
	??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@std@@@1@@Z
	??$_Erase_tree@V?$allocator@U?$_Tree_node@HPEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@H@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@HPEAX@std@@@1@PEAU?$_Tree_node@HPEAX@1@@Z
	??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@1@@Z
	??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@QEAXV?$RefCounterPtr@VITexture2D@Graphics@@@Graphics@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@QEAXV?$RefCounterPtr@VITexture2D@Graphics@@@Graphics@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@QEAXV?$RefCounterPtr@VITexture2D@Graphics@@@Graphics@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@QEAXV?$RefCounterPtr@VITexture2D@Graphics@@@Graphics@@@std@@PEAX@1@@Z
	??$_Erase_tree@V?$polymorphic_allocator@U?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@@pmr@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@@std@@@std@@QEAAXAEAV?$polymorphic_allocator@U?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@@pmr@1@PEAU?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@1@@Z
	??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@1@@Z
	??$_Fill_memset@PEAEE@std@@YAXPEAEE_K@Z
	??$_Fill_memset@PEAEH@std@@YAXPEAEH_K@Z
	??$_Fill_zero_memset@PEAI@std@@YAXPEAI_K@Z
	??$_Fill_zero_memset@PEAM@std@@YAXPEAM_K@Z
	??$_Fill_zero_memset@PEA_K@std@@YAXPEA_K_K@Z
	??$_Find_last@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@HV?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@2@$0A@@std@@@std@@IEBA?AU?$_Hash_find_last_result@PEAU?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@PEAX@std@@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@_K@Z
	??$_Find_last@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@2@$0A@@std@@@std@@IEBA?AU?$_Hash_find_last_result@PEAU?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@PEAX@std@@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@_K@Z
	??$_Find_last@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@2@$0A@@std@@@std@@IEBA?AU?$_Hash_find_last_result@PEAU?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@PEAX@std@@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@_K@Z
	??$_Find_last@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@?$_Hash@V?$_Uset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@IEBA?AU?$_Hash_find_last_result@PEAU?$_List_node@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAX@std@@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@_K@Z
	??$_Find_last@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@?$_Hash@V?$_Umap_traits@V?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@V?$_Uhash_compare@V?$basic_string_view@GU?$char_traits@G@std@@@std@@U?$hash@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@U?$equal_to@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@2@$0A@@std@@@std@@IEBA?AU?$_Hash_find_last_result@PEAU?$_List_node@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@PEAX@std@@@1@AEBV?$basic_string_view@GU?$char_traits@G@std@@@1@_K@Z
	??$_Find_vectorized@$$CB_W_W@std@@YAPEB_WQEB_W0_W@Z
	??$_Find_vectorized@DD@std@@YAPEADQEAD0D@Z
	??$_Fnv1a_append_value@G@std@@YA_K_KAEBG@Z
	??$_Fnv1a_append_value@H@std@@YA_K_KAEBH@Z
	??$_Fnv1a_append_value@W4ScriptType@UniText@@@std@@YA_K_KAEBW4ScriptType@UniText@@@Z
	??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@PEAX@std@@@1@PEAU01@@Z
	??$_Free_non_head@V?$allocator@U?$_List_node@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAX@std@@@std@@@?$_List_node@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAX@std@@@1@PEAU01@@Z
	??$_Freenode@V?$allocator@U?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@PEAX@std@@@1@PEAU01@@Z
	??$_Getvals@_W@?$time_get@DV?$istreambuf_iterator@DU?$char_traits@D@std@@@std@@@std@@IEAAX_WAEBV_Locinfo@1@@Z
	??$_Getvals@_W@?$time_get@_WV?$istreambuf_iterator@_WU?$char_traits@_W@std@@@std@@@std@@IEAAX_WAEBV_Locinfo@1@@Z
	??$_Hash_array_representation@D@std@@YA_KQEBD_K@Z
	??$_Hash_representation@G@std@@YA_KAEBG@Z
	??$_Hash_representation@H@std@@YA_KAEBH@Z
	??$_Hash_representation@W4ScriptType@UniText@@@std@@YA_KAEBW4ScriptType@UniText@@@Z
	??$_Immortalize_memcpy_image@V_Generic_error_category@std@@@std@@YAAEBV_Generic_error_category@0@XZ
	??$_Immortalize_memcpy_image@V_System_error_category@std@@@std@@YAAEBV_System_error_category@0@XZ
	??$_Initialize_dir_enum@U_Dir_enum_impl@filesystem@std@@@_Dir_enum_impl@filesystem@std@@SA?AW4__std_win_error@@AEAV?$shared_ptr@U_Dir_enum_impl@filesystem@std@@@2@AEBVpath@12@W4directory_options@12@@Z
	??$_Initialize_dir_enum@U_Recursive_dir_enum_impl@filesystem@std@@@_Dir_enum_impl@filesystem@std@@SA?AW4__std_win_error@@AEAV?$shared_ptr@U_Recursive_dir_enum_impl@filesystem@std@@@2@AEBVpath@12@W4directory_options@12@@Z
	??$_Insert_counted_range@PEAUFontFallbackInfo@UniText@@@?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UFontFallbackInfo@UniText@@@std@@@std@@@1@PEAUFontFallbackInfo@UniText@@_K@Z
	??$_Insert_range_unchecked@V?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@std@@@std@@U_Iterator_base0@2@@std@@V12@@?$_Hash@V?$_Umap_traits@V?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@V?$_Uhash_compare@V?$basic_string_view@GU?$char_traits@G@std@@@std@@U?$hash@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@U?$equal_to@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@2@$0A@@std@@@std@@IEAAXV?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@std@@@std@@U_Iterator_base0@2@@1@V21@@Z
	??$_Is_all_bits_zero@I@std@@YA_NAEBI@Z
	??$_Is_all_bits_zero@M@std@@YA_NAEBM@Z
	??$_Is_all_bits_zero@_K@std@@YA_NAEB_K@Z
	??$_Move_unchecked@PEAUFontFallbackInfo@UniText@@PEAU12@@std@@YAPEAUFontFallbackInfo@UniText@@PEAU12@00@Z
	??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z
	??$_Reallocate_grow_by@V<lambda_1dfe18491bcca09701d8ccb01d0b0af4>@@PEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_1dfe18491bcca09701d8ccb01d0b0af4>@@PEB_W_K@Z
	??$_Reallocate_grow_by@V<lambda_25953b27f3c43b57ba59f021c7f225c5>@@_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_25953b27f3c43b57ba59f021c7f225c5>@@_W@Z
	??$_Reallocate_grow_by@V<lambda_319d5e083f45f90dcdce5dce53cbb275>@@D@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_319d5e083f45f90dcdce5dce53cbb275>@@D@Z
	??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z
	??$_Reallocate_grow_by@V<lambda_9013ee9e23efe4882b67eff5b0ecf103>@@$$V@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_9013ee9e23efe4882b67eff5b0ecf103>@@@Z
	??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z
	??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z
	??$_Remove_vectorized@DD@std@@YAPEADQEAD0D@Z
	??$_Resize@E@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBE@Z
	??$_Resize@U?$Vector2@M@UniText@@@?$vector@U?$Vector2@M@UniText@@V?$allocator@U?$Vector2@M@UniText@@@std@@@std@@AEAAX_KAEBU?$Vector2@M@UniText@@@Z
	??$_Resize@U_Value_init_tag@std@@@?$vector@ULine@UniTextGenerator@UniText@@V?$polymorphic_allocator@ULine@UniTextGenerator@UniText@@@pmr@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z
	??$_Resize_reallocate@I@?$vector@IV?$allocator@I@std@@@std@@AEAAX_KAEBI@Z
	??$_Search_vectorized@$$CBD$$CBD@std@@YAPEBDQEBD00_K@Z
	??$_Stringoid_from_Source@DU?$char_traits@D@std@@V?$allocator@D@2@@filesystem@std@@YA?A_PAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@@Z
	??$_Stringoid_from_Source@_WU?$char_traits@_W@std@@@filesystem@std@@YA?A_PAEBV?$basic_string_view@_WU?$char_traits@_W@std@@@1@@Z
	??$_Try_emplace@AEBH$$V@?$_Hash@V?$_Umap_traits@HV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@V?$_Uhash_compare@HU?$hash@H@std@@U?$equal_to@H@2@@2@V?$allocator@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@PEAX@std@@_N@1@AEBH@Z
	??$_Try_emplace@AEBH$$V@?$_Hash@V?$_Umap_traits@HV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@V?$_Uhash_compare@HU?$hash@H@std@@U?$equal_to@H@2@@2@V?$allocator@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@PEAX@std@@_N@1@AEBH@Z
	??$_Try_emplace@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$V@?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@PEAX@std@@_N@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@@Z
	??$_Try_emplace@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$V@?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@PEAX@std@@_N@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@@Z
	??$_Try_emplace@AEBV?$basic_string_view@GU?$char_traits@G@std@@@std@@$$V@?$_Hash@V?$_Umap_traits@V?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@V?$_Uhash_compare@V?$basic_string_view@GU?$char_traits@G@std@@@std@@U?$hash@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@U?$equal_to@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@PEAX@std@@_N@1@AEBV?$basic_string_view@GU?$char_traits@G@std@@@1@@Z
	??$_Try_emplace@G$$V@?$_Hash@V?$_Umap_traits@GUGlyphInfo@UniText@@V?$_Uhash_compare@GU?$hash@G@std@@U?$equal_to@G@2@@std@@V?$allocator@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@PEAX@std@@_N@1@$$QEAG@Z
	??$_Try_emplace@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$V@?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@HV?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@2@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@PEAX@std@@_N@1@$$QEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@@Z
	??$_Try_emplace@W4ScriptType@UniText@@$$V@?$_Hash@V?$_Umap_traits@W4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@V?$_Uhash_compare@W4ScriptType@UniText@@U?$hash@W4ScriptType@UniText@@@std@@U?$equal_to@W4ScriptType@UniText@@@4@@4@V?$allocator@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_List_node@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@PEAX@std@@_N@1@$$QEAW4ScriptType@UniText@@@Z
	??$_Unaligned_load@I@filesystem@std@@YAIPEBX@Z
	??$_Uninitialized_copy@PEAUColor@UniText@@PEAU12@V?$allocator@UColor@UniText@@@std@@@std@@YAPEAUColor@UniText@@PEAU12@00AEAV?$allocator@UColor@UniText@@@0@@Z
	??$_Uninitialized_copy_n@PEAUFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@YAPEAUFontFallbackInfo@UniText@@PEAU12@_K0AEAV?$allocator@UFontFallbackInfo@UniText@@@0@@Z
	??$_Uninitialized_fill_n@V?$allocator@M@std@@@std@@YAPEAMPEAM_KAEBMAEAV?$allocator@M@0@@Z
	??$_Uninitialized_move@PEAUColor@UniText@@V?$allocator@UColor@UniText@@@std@@@std@@YAPEAUColor@UniText@@QEAU12@0PEAU12@AEAV?$allocator@UColor@UniText@@@0@@Z
	??$_Uninitialized_move@PEAUFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@YAPEAUFontFallbackInfo@UniText@@QEAU12@0PEAU12@AEAV?$allocator@UFontFallbackInfo@UniText@@@0@@Z
	??$_Uninitialized_move@PEAUGlyphLookUpTable@UniText@@V?$allocator@UGlyphLookUpTable@UniText@@@std@@@std@@YAPEAUGlyphLookUpTable@UniText@@QEAU12@0PEAU12@AEAV?$allocator@UGlyphLookUpTable@UniText@@@0@@Z
	??$_Uninitialized_move@PEAULayer@ShelfGlyphPacker@UniText@@V?$polymorphic_allocator@ULayer@ShelfGlyphPacker@UniText@@@pmr@std@@@std@@YAPEAULayer@ShelfGlyphPacker@UniText@@QEAU123@0PEAU123@AEAV?$polymorphic_allocator@ULayer@ShelfGlyphPacker@UniText@@@pmr@0@@Z
	??$_Uninitialized_move@PEAURichTextTag@UniTextGenerator@UniText@@V?$allocator@URichTextTag@UniTextGenerator@UniText@@@std@@@std@@YAPEAURichTextTag@UniTextGenerator@UniText@@QEAU123@0PEAU123@AEAV?$allocator@URichTextTag@UniTextGenerator@UniText@@@0@@Z
	??$_Uninitialized_move@PEAUSizedPage@UniText@@V?$polymorphic_allocator@USizedPage@UniText@@@pmr@std@@@std@@YAPEAUSizedPage@UniText@@QEAU12@0PEAU12@AEAV?$polymorphic_allocator@USizedPage@UniText@@@pmr@0@@Z
	??$_Uninitialized_move@PEAU_Pool@unsynchronized_pool_resource@pmr@std@@V?$polymorphic_allocator@U_Pool@unsynchronized_pool_resource@pmr@std@@@34@@std@@YAPEAU_Pool@unsynchronized_pool_resource@pmr@0@QEAU1230@0PEAU1230@AEAV?$polymorphic_allocator@U_Pool@unsynchronized_pool_resource@pmr@std@@@30@@Z
	??$_Uninitialized_move@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@YAPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@0@@Z
	??$_Uninitialized_value_construct_n_unchecked1@PEAPEAH_K@std@@YAPEAPEAHPEAPEAH_K@Z
	??$_Uninitialized_value_construct_n_unchecked1@PEAPEAUSdf_RenderData@UniText@@_K@std@@YAPEAPEAUSdf_RenderData@UniText@@PEAPEAU12@_K@Z
	??$_Uses_alloc_construct_non_pair@GV?$allocator@D@std@@V?$polymorphic_allocator@G@pmr@2@AEBG@std@@YAXQEAGAEAV?$allocator@D@0@AEAV?$polymorphic_allocator@G@pmr@0@AEBG@Z
	??$_Uses_alloc_construct_non_pair@GV?$allocator@D@std@@V?$polymorphic_allocator@G@pmr@2@G@std@@YAXQEAGAEAV?$allocator@D@0@AEAV?$polymorphic_allocator@G@pmr@0@$$QEAG@Z
	??$_Uses_alloc_construct_non_pair@ULayer@ShelfGlyphPacker@UniText@@V?$allocator@D@std@@V?$polymorphic_allocator@ULayer@ShelfGlyphPacker@UniText@@@pmr@5@U123@@std@@YAXQEAULayer@ShelfGlyphPacker@UniText@@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@ULayer@ShelfGlyphPacker@UniText@@@pmr@0@$$QEAU123@@Z
	??$_Uses_alloc_construct_non_pair@ULetter@UniTextGenerator@UniText@@V?$allocator@D@std@@V?$polymorphic_allocator@ULetter@UniTextGenerator@UniText@@@pmr@5@U123@@std@@YAXQEAULetter@UniTextGenerator@UniText@@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@ULetter@UniTextGenerator@UniText@@@pmr@0@$$QEAU123@@Z
	??$_Uses_alloc_construct_non_pair@ULine@UniTextGenerator@UniText@@V?$allocator@D@std@@V?$polymorphic_allocator@ULine@UniTextGenerator@UniText@@@pmr@5@$$V@std@@YAXQEAULine@UniTextGenerator@UniText@@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@ULine@UniTextGenerator@UniText@@@pmr@0@@Z
	??$_Uses_alloc_construct_non_pair@ULine@UniTextGenerator@UniText@@V?$allocator@D@std@@V?$polymorphic_allocator@ULine@UniTextGenerator@UniText@@@pmr@5@AEBU123@@std@@YAXQEAULine@UniTextGenerator@UniText@@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@ULine@UniTextGenerator@UniText@@@pmr@0@AEBU123@@Z
	??$_Uses_alloc_construct_non_pair@ULine@UniTextGenerator@UniText@@V?$allocator@D@std@@V?$polymorphic_allocator@ULine@UniTextGenerator@UniText@@@pmr@5@U123@@std@@YAXQEAULine@UniTextGenerator@UniText@@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@ULine@UniTextGenerator@UniText@@@pmr@0@$$QEAU123@@Z
	??$_Uses_alloc_construct_non_pair@ULineDecoration@UniTextGenerator@UniText@@V?$allocator@D@std@@V?$polymorphic_allocator@ULineDecoration@UniTextGenerator@UniText@@@pmr@5@AEAU123@@std@@YAXQEAULineDecoration@UniTextGenerator@UniText@@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@ULineDecoration@UniTextGenerator@UniText@@@pmr@0@AEAU123@@Z
	??$_Uses_alloc_construct_non_pair@ULineDecoration@UniTextGenerator@UniText@@V?$allocator@D@std@@V?$polymorphic_allocator@ULineDecoration@UniTextGenerator@UniText@@@pmr@5@U123@@std@@YAXQEAULineDecoration@UniTextGenerator@UniText@@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@ULineDecoration@UniTextGenerator@UniText@@@pmr@0@$$QEAU123@@Z
	??$_Uses_alloc_construct_non_pair@URange@ShelfGlyphPacker@UniText@@V?$allocator@D@std@@V?$polymorphic_allocator@URange@ShelfGlyphPacker@UniText@@@pmr@5@AEBU123@@std@@YAXQEAURange@ShelfGlyphPacker@UniText@@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@URange@ShelfGlyphPacker@UniText@@@pmr@0@AEBU123@@Z
	??$_Uses_alloc_construct_non_pair@URange@ShelfGlyphPacker@UniText@@V?$allocator@D@std@@V?$polymorphic_allocator@URange@ShelfGlyphPacker@UniText@@@pmr@5@U123@@std@@YAXQEAURange@ShelfGlyphPacker@UniText@@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@URange@ShelfGlyphPacker@UniText@@@pmr@0@$$QEAU123@@Z
	??$_Uses_alloc_construct_non_pair@USizedPage@UniText@@V?$allocator@D@std@@V?$polymorphic_allocator@USizedPage@UniText@@@pmr@4@U12@@std@@YAXQEAUSizedPage@UniText@@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@USizedPage@UniText@@@pmr@0@$$QEAU12@@Z
	??$_Uses_alloc_construct_non_pair@UWord@UniTextGenerator@UniText@@V?$allocator@D@std@@V?$polymorphic_allocator@UWord@UniTextGenerator@UniText@@@pmr@5@AEAU123@@std@@YAXQEAUWord@UniTextGenerator@UniText@@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@UWord@UniTextGenerator@UniText@@@pmr@0@AEAU123@@Z
	??$_Uses_alloc_construct_non_pair@UWord@UniTextGenerator@UniText@@V?$allocator@D@std@@V?$polymorphic_allocator@UWord@UniTextGenerator@UniText@@@pmr@5@U123@@std@@YAXQEAUWord@UniTextGenerator@UniText@@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@UWord@UniTextGenerator@UniText@@@pmr@0@$$QEAU123@@Z
	??$_Uses_alloc_construct_non_pair@U_Pool@unsynchronized_pool_resource@pmr@std@@V?$allocator@D@4@V?$polymorphic_allocator@U_Pool@unsynchronized_pool_resource@pmr@std@@@34@AEAE@std@@YAXQEAU_Pool@unsynchronized_pool_resource@pmr@0@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@U_Pool@unsynchronized_pool_resource@pmr@std@@@30@AEAE@Z
	??$_Uses_alloc_construct_non_pair@U_Pool@unsynchronized_pool_resource@pmr@std@@V?$allocator@D@4@V?$polymorphic_allocator@U_Pool@unsynchronized_pool_resource@pmr@std@@@34@U1234@@std@@YAXQEAU_Pool@unsynchronized_pool_resource@pmr@0@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@U_Pool@unsynchronized_pool_resource@pmr@std@@@30@$$QEAU1230@@Z
	??$_Uses_alloc_construct_pair@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@V?$allocator@D@2@V?$polymorphic_allocator@U?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@@pmr@2@UGlyphLookUpKey@UniText@@UGlyphInfo@7@@std@@YAXQEAU?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@0@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@U?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@@pmr@0@$$QEAUGlyphLookUpKey@UniText@@$$QEAUGlyphInfo@6@@Z
	??$_Uses_alloc_construct_pair_piecewise@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@V?$allocator@D@2@V?$polymorphic_allocator@U?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@@pmr@2@$$QEAUGlyphLookUpKey@UniText@@$$Z$$QEAUGlyphInfo@7@@std@@YAXQEAU?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@0@AEAV?$allocator@D@0@AEAV?$polymorphic_allocator@U?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@@pmr@0@$$QEAV?$tuple@$$QEAUGlyphLookUpKey@UniText@@@0@$$QEAV?$tuple@$$QEAUGlyphInfo@UniText@@@0@@Z
	??$_Uses_alloc_piecewise@$$CBUGlyphLookUpKey@UniText@@V?$polymorphic_allocator@U?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@@pmr@std@@$$QEAU12@@std@@YA?A_TAEAV?$polymorphic_allocator@U?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@@pmr@0@$$QEAV?$tuple@$$QEAUGlyphLookUpKey@UniText@@@0@@Z
	??$_Uses_alloc_piecewise@UGlyphInfo@UniText@@V?$polymorphic_allocator@U?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@@pmr@std@@$$QEAU12@@std@@YA?A_TAEAV?$polymorphic_allocator@U?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@@pmr@0@$$QEAV?$tuple@$$QEAUGlyphInfo@UniText@@@0@@Z
	??$_Zero_range@PEAPEAH@std@@YAPEAPEAHQEAPEAH0@Z
	??$_Zero_range@PEAPEAUSdf_RenderData@UniText@@@std@@YAPEAPEAUSdf_RenderData@UniText@@QEAPEAU12@0@Z
	??$emplace@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@?$_Hash@V?$_Uset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QEAA?AU?$pair@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@@std@@_N@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@@Z
	??$emplace_back@USizedPage@UniText@@@?$vector@USizedPage@UniText@@V?$polymorphic_allocator@USizedPage@UniText@@@pmr@std@@@std@@QEAAAEAUSizedPage@UniText@@$$QEAU23@@Z
	??$erase@V?$_List_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@std@@@std@@@std@@$0A@@?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@HV?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@2@$0A@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@std@@@std@@@1@V21@@Z
	??$fill@PEAV?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@U_Iterator_base0@2@@std@@V12@@std@@YAXQEAV?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@U_Iterator_base0@2@@0@0AEBV10@@Z
	??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@std@@@std@@@0@0AEBV10@@Z
	??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$find@X@?$_Hash@V?$_Umap_traits@HV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@V?$_Uhash_compare@HU?$hash@H@std@@U?$equal_to@H@2@@2@V?$allocator@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@@2@$0A@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@@std@@@std@@@1@AEBH@Z
	??$transform@V?$_String_iterator@V?$_String_val@U?$_Simple_types@D@std@@@std@@@std@@V12@P6AHH@Z@std@@YA?AV?$_String_iterator@V?$_String_val@U?$_Simple_types@D@std@@@std@@@0@V10@0V10@P6AHH@Z@Z
	??$uninitialized_copy@PEAPEAHPEAPEAH@std@@YAPEAPEAHQEAPEAH0PEAPEAH@Z
	??$uninitialized_copy@PEAPEAUSdf_RenderData@UniText@@PEAPEAU12@@std@@YAPEAPEAUSdf_RenderData@UniText@@QEAPEAU12@0PEAPEAU12@@Z
	??$uninitialized_fill@PEAV?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@U_Iterator_base0@2@@std@@V12@@std@@YAXQEAV?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@U_Iterator_base0@2@@0@0AEBV10@@Z
	??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@std@@@std@@@0@0AEBV10@@Z
	??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@@std@@@std@@@0@0AEBV10@@Z
	??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBDPEAVUniFont@UniText@@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBDPEAVUniFont@UniText@@@std@@@std@@@std@@@0@0AEBV10@@Z
	??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z
	??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z
	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
	??0?$unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@2@@std@@QEAA@XZ
	??0?$unordered_set@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ
	??0?$vector@HV?$allocator@H@std@@@std@@QEAA@AEBV01@@Z
	??0?$vector@URange@ShelfGlyphPacker@UniText@@V?$polymorphic_allocator@URange@ShelfGlyphPacker@UniText@@@pmr@std@@@std@@QEAA@V?$initializer_list@URange@ShelfGlyphPacker@UniText@@@1@AEBV?$polymorphic_allocator@URange@ShelfGlyphPacker@UniText@@@pmr@1@@Z
	??0?$vector@URichTextTag@UniTextGenerator@UniText@@V?$allocator@URichTextTag@UniTextGenerator@UniText@@@std@@@std@@QEAA@XZ
	??0?$vector@USdfVertex@UniText@@V?$allocator@USdfVertex@UniText@@@std@@@std@@QEAA@AEBV01@@Z
	??0?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@V?$initializer_list@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@AEBV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@@Z
	??0AndroidFontFallbackProvider@UniText@@QEAA@XZ
	??0AppleFontFallbackProvider@UniText@@QEAA@XZ
	??0DefaultFontFallbackProvider@UniText@@QEAA@XZ
	??0FontFallbackInfo@UniText@@QEAA@AEBU01@@Z
	??0FontFallbackInfo@UniText@@QEAA@XZ
	??0FreeTypeContext@UniText@@QEAA@XZ
	??0GlyphLookUpTable@UniText@@QEAA@$$QEAU01@@Z
	??0RichTextTag@UniTextGenerator@UniText@@QEAA@$$QEAU012@@Z
	??0SDFGenerator@UniText@@QEAA@XZ
	??0SdfPipeline@UniText@@QEAA@XZ
	??0Sdf_RenderData@UniText@@QEAA@AEBU01@@Z
	??0UniCustomFont@UniText@@QEAA@XZ
	??0UniFont@UniText@@QEAA@PEBDV?$unique_ptr@UIUniFontImpl@UniText@@U?$default_delete@UIUniFontImpl@UniText@@@std@@@std@@@Z
	??0UniFontCache@UniText@@QEAA@XZ
	??0UniFontFallbackCache@UniText@@QEAA@XZ
	??0UniFontFreeType@UniText@@QEAA@XZ
	??0UniObject@UniText@@IEAA@PEBD@Z
	??0UniObject@UniText@@IEAA@PEBDH@Z
	??0UniTextGenerator@UniText@@QEAA@XZ
	??0UniTextureNative@UniText@@QEAA@GGW4Format@ITexture@1@@Z
	??0UnifiedTextProcessor@UniText@@QEAA@XZ
	??0VSDFGenerator@UniText@@QEAA@XZ
	??0WindowsFontFallbackProvider@UniText@@QEAA@XZ
	??0_Dir_enum_impl@filesystem@std@@QEAA@$$QEAU_Creator@012@W4directory_options@12@@Z
	??0_System_error@std@@IEAA@Verror_code@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@@Z
	??0_System_error@std@@QEAA@AEBV01@@Z
	??0bad_alloc@std@@QEAA@AEBV01@@Z
	??0bad_array_new_length@std@@QEAA@AEBV01@@Z
	??0bad_array_new_length@std@@QEAA@XZ
	??0bad_optional_access@std@@QEAA@AEBV01@@Z
	??0bad_optional_access@std@@QEAA@XZ
	??0bad_variant_access@std@@QEAA@AEBV01@@Z
	??0bad_variant_access@std@@QEAA@XZ
	??0directory_iterator@filesystem@std@@QEAA@AEBV012@@Z
	??0directory_iterator@filesystem@std@@QEAA@AEBVpath@12@@Z
	??0exception@std@@QEAA@AEBV01@@Z
	??0filesystem_error@filesystem@std@@QEAA@AEBV012@@Z
	??0filesystem_error@filesystem@std@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@AEBVpath@12@Verror_code@2@@Z
	??0filesystem_error@filesystem@std@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@Verror_code@2@@Z
	??0runtime_error@std@@QEAA@AEBV01@@Z
	??0system_error@std@@QEAA@AEBV01@@Z
	??0system_error@std@@QEAA@Verror_code@1@@Z
	??1?$RefCounterPtr@VIProgram@Graphics@@@Graphics@@QEAA@XZ
	??1?$RefCounterPtr@VIShader@Graphics@@@Graphics@@QEAA@XZ
	??1?$RefCounterPtr@VITexture2D@Graphics@@@Graphics@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@HPEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@QEAXV?$RefCounterPtr@VITexture2D@Graphics@@@Graphics@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Alloc_construct_ptr@V?$polymorphic_allocator@U?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@@pmr@std@@@std@@QEAA@XZ
	??1?$_Erase_tree_and_orphan_guard@V?$allocator@U?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@std@@@std@@QEAA@XZ
	??1?$_Hash@V?$_Umap_traits@GUGlyphInfo@UniText@@V?$_Uhash_compare@GU?$hash@G@std@@U?$equal_to@G@2@@std@@V?$allocator@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@@4@$0A@@std@@@std@@QEAA@XZ
	??1?$_Hash@V?$_Umap_traits@HV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@V?$_Uhash_compare@HU?$hash@H@std@@U?$equal_to@H@2@@2@V?$allocator@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@@2@$0A@@std@@@std@@QEAA@XZ
	??1?$_Hash@V?$_Umap_traits@PEBDPEAVUniFont@UniText@@V?$_Uhash_compare@PEBDU?$hash@PEBD@std@@U?$equal_to@PEBD@2@@std@@V?$allocator@U?$pair@QEBDPEAVUniFont@UniText@@@std@@@4@$0A@@std@@@std@@QEAA@XZ
	??1?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@HV?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@2@$0A@@std@@@std@@QEAA@XZ
	??1?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@2@$0A@@std@@@std@@QEAA@XZ
	??1?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@2@$0A@@std@@@std@@QEAA@XZ
	??1?$_Hash@V?$_Umap_traits@V?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@V?$_Uhash_compare@V?$basic_string_view@GU?$char_traits@G@std@@@std@@U?$hash@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@U?$equal_to@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@2@$0A@@std@@@std@@QEAA@XZ
	??1?$_Hash@V?$_Umap_traits@W4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@V?$_Uhash_compare@W4ScriptType@UniText@@U?$hash@W4ScriptType@UniText@@@std@@U?$equal_to@W4ScriptType@UniText@@@4@@4@V?$allocator@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@@4@$0A@@std@@@std@@QEAA@XZ
	??1?$_Hash@V?$_Uset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QEAA@XZ
	??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@U_Iterator_base0@2@@std@@@std@@@std@@QEAA@XZ
	??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ
	??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ
	??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ
	??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ
	??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ
	??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ
	??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ
	??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@QEBDPEAVUniFont@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ
	??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_List_node_emplace_op2@V?$allocator@U?$_List_node@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Ref_count_obj2@U_Dir_enum_impl@filesystem@std@@@std@@UEAA@XZ
	??1?$_Ref_count_obj2@U_Recursive_dir_enum_impl@filesystem@std@@@std@@UEAA@XZ
	??1?$_Tidy_guard@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@QEAA@XZ
	??1?$_Tidy_guard@V?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@std@@QEAA@XZ
	??1?$_Tree@V?$_Tmap_traits@UGlyphLookUpKey@UniText@@UGlyphInfo@2@U?$less@UGlyphLookUpKey@UniText@@@std@@V?$polymorphic_allocator@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@@pmr@5@$0A@@std@@@std@@QEAA@XZ
	??1?$_Tree_head_scoped_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@std@@@2@@std@@QEAA@XZ
	??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@HPEAX@std@@@std@@@std@@QEAA@XZ
	??1?$_Uninitialized_backout_al@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@QEAA@XZ
	??1?$_Uninitialized_backout_al@V?$allocator@UGlyphLookUpTable@UniText@@@std@@@std@@QEAA@XZ
	??1?$_Uninitialized_backout_al@V?$allocator@URichTextTag@UniTextGenerator@UniText@@@std@@@std@@QEAA@XZ
	??1?$_Uninitialized_backout_al@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@QEAA@XZ
	??1?$array@UVertexAttribute@Graphics@@$04@std@@QEAA@XZ
	??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
	??1?$basic_string@DU?$char_traits@D@std@@V?$polymorphic_allocator@D@pmr@2@@std@@QEAA@XZ
	??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
	??1?$deque@HV?$allocator@H@std@@@std@@QEAA@XZ
	??1?$deque@USdf_RenderData@UniText@@V?$allocator@USdf_RenderData@UniText@@@std@@@std@@QEAA@XZ
	??1?$function@$$A6AXAEBU?$Rect@G@UniText@@_N@Z@std@@QEAA@XZ
	??1?$function@$$A6AXPEAVUniFont@UniText@@@Z@std@@QEAA@XZ
	??1?$list@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@V?$allocator@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@@2@@std@@QEAA@XZ
	??1?$list@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@V?$allocator@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@@2@@std@@QEAA@XZ
	??1?$list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@2@@std@@QEAA@XZ
	??1?$list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@2@@std@@QEAA@XZ
	??1?$list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@2@@std@@QEAA@XZ
	??1?$list@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@V?$allocator@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@2@@std@@QEAA@XZ
	??1?$list@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@V?$allocator@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@@2@@std@@QEAA@XZ
	??1?$list@U?$pair@QEBDPEAVUniFont@UniText@@@std@@V?$allocator@U?$pair@QEBDPEAVUniFont@UniText@@@std@@@2@@std@@QEAA@XZ
	??1?$list@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ
	??1?$lock_guard@Vmutex@std@@@std@@QEAA@XZ
	??1?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@std@@QEAA@XZ
	??1?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@QEAA@XZ
	??1?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@QEAA@XZ
	??1?$queue@HV?$deque@HV?$allocator@H@std@@@std@@@std@@QEAA@XZ
	??1?$queue@USdf_RenderData@UniText@@V?$deque@USdf_RenderData@UniText@@V?$allocator@USdf_RenderData@UniText@@@std@@@std@@@std@@QEAA@XZ
	??1?$set@HU?$less@H@std@@V?$allocator@H@2@@std@@QEAA@XZ
	??1?$shared_ptr@U_Dir_enum_impl@filesystem@std@@@std@@QEAA@XZ
	??1?$shared_ptr@U_Recursive_dir_enum_impl@filesystem@std@@@std@@QEAA@XZ
	??1?$unique_ptr@UCustomFontFace@UniCustomFont@UniText@@U?$default_delete@UCustomFontFace@UniCustomFont@UniText@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@UIUniFontImpl@UniText@@U?$default_delete@UIUniFontImpl@UniText@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@VIFontFallbackProvider@UniText@@U?$default_delete@VIFontFallbackProvider@UniText@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@VIUniGlyphPacker@UniText@@U?$default_delete@VIUniGlyphPacker@UniText@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@VIUniPostProcessor@UniText@@U?$default_delete@VIUniPostProcessor@UniText@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@VUniFontAtlasEntry@UniText@@U?$default_delete@VUniFontAtlasEntry@UniText@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@VUniTextureNative@UniText@@U?$default_delete@VUniTextureNative@UniText@@@std@@@std@@QEAA@XZ
	??1?$unique_ptr@VUnifiedTextProcessor@UniText@@U?$default_delete@VUnifiedTextProcessor@UniText@@@std@@@std@@QEAA@XZ
	??1?$unordered_map@HV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@U?$hash@H@2@U?$equal_to@H@2@V?$allocator@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@@2@@std@@QEAA@XZ
	??1?$unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@2@@std@@QEAA@XZ
	??1?$unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@2@@std@@QEAA@XZ
	??1?$unordered_set@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ
	??1?$vector@EV?$allocator@E@std@@@std@@QEAA@XZ
	??1?$vector@GV?$polymorphic_allocator@G@pmr@std@@@std@@QEAA@XZ
	??1?$vector@PEAVUniFont@UniText@@V?$allocator@PEAVUniFont@UniText@@@std@@@std@@QEAA@XZ
	??1?$vector@U?$IndexedQuadTree@$06@UniText@@V?$allocator@U?$IndexedQuadTree@$06@UniText@@@std@@@std@@QEAA@XZ
	??1?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@QEAA@XZ
	??1?$vector@UGlyphLookUpTable@UniText@@V?$allocator@UGlyphLookUpTable@UniText@@@std@@@std@@QEAA@XZ
	??1?$vector@ULayer@ShelfGlyphPacker@UniText@@V?$polymorphic_allocator@ULayer@ShelfGlyphPacker@UniText@@@pmr@std@@@std@@QEAA@XZ
	??1?$vector@ULetter@UniTextGenerator@UniText@@V?$polymorphic_allocator@ULetter@UniTextGenerator@UniText@@@pmr@std@@@std@@QEAA@XZ
	??1?$vector@ULine@UniTextGenerator@UniText@@V?$polymorphic_allocator@ULine@UniTextGenerator@UniText@@@pmr@std@@@std@@QEAA@XZ
	??1?$vector@ULineDecoration@UniTextGenerator@UniText@@V?$polymorphic_allocator@ULineDecoration@UniTextGenerator@UniText@@@pmr@std@@@std@@QEAA@XZ
	??1?$vector@URichTextTag@UniTextGenerator@UniText@@V?$allocator@URichTextTag@UniTextGenerator@UniText@@@std@@@std@@QEAA@XZ
	??1?$vector@USdfVertex@UniText@@V?$allocator@USdfVertex@UniText@@@std@@@std@@QEAA@XZ
	??1?$vector@USizedPage@UniText@@V?$polymorphic_allocator@USizedPage@UniText@@@pmr@std@@@std@@QEAA@XZ
	??1?$vector@U_Find_file_handle@filesystem@std@@V?$allocator@U_Find_file_handle@filesystem@std@@@3@@std@@QEAA@XZ
	??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ
	??1?$vector@V?$unique_ptr@VITexture@UniText@@U?$default_delete@VITexture@UniText@@@std@@@std@@V?$allocator@V?$unique_ptr@VITexture@UniText@@U?$default_delete@VITexture@UniText@@@std@@@std@@@2@@std@@QEAA@XZ
	??1?$vector@W4FontStyle@UniText@@V?$allocator@W4FontStyle@UniText@@@std@@@std@@QEAA@XZ
	??1?$vector@W4ScriptType@UniText@@V?$allocator@W4ScriptType@UniText@@@std@@@std@@QEAA@XZ
	??1AndroidFontFallbackProvider@UniText@@UEAA@XZ
	??1AppleFontFallbackProvider@UniText@@UEAA@XZ
	??1BufferArrayDesc@Graphics@@QEAA@XZ
	??1DefaultFontFallbackProvider@UniText@@UEAA@XZ
	??1DrawParams@Graphics@@QEAA@XZ
	??1EDTAA_Grid@UniText@@QEAA@XZ
	??1FontFallbackInfo@UniText@@QEAA@XZ
	??1FreeTypeContext@UniText@@QEAA@XZ
	??1GlyphLookUpTable@UniText@@QEAA@XZ
	??1IFontFallbackProvider@UniText@@UEAA@XZ
	??1ITextProcessor@UniText@@UEAA@XZ
	??1ITexture@UniText@@UEAA@XZ
	??1IUniFontImpl@UniText@@UEAA@XZ
	??1IUniGlyphPacker@UniText@@UEAA@XZ
	??1IUniPostProcessor@UniText@@UEAA@XZ
	??1Layer@ShelfGlyphPacker@UniText@@QEAA@XZ
	??1MeshData@UniTextGenerator@UniText@@QEAA@XZ
	??1PipelineDesc@Graphics@@QEAA@XZ
	??1ProgramDesc@Graphics@@QEAA@XZ
	??1PropertyModifier@UniTextGenerator@UniText@@QEAA@XZ
	??1RichTextTag@UniTextGenerator@UniText@@QEAA@XZ
	??1RichTextTagParseResult@@QEAA@XZ
	??1SdfPipeline@UniText@@QEAA@XZ
	??1Sdf_FillPass@UniText@@QEAA@XZ
	??1Sdf_LinePass@UniText@@QEAA@XZ
	??1Sdf_RenderData@UniText@@QEAA@XZ
	??1ShaderDesc@Graphics@@QEAA@XZ
	??1SimpleTextProcessor@UniText@@UEAA@XZ
	??1SizedPage@UniText@@QEAA@XZ
	??1UTF16String@UniText@@QEAA@XZ
	??1UniCustomFont@UniText@@UEAA@XZ
	??1UniFont@UniText@@UEAA@XZ
	??1UniFontFallbackCache@UniText@@QEAA@XZ
	??1UniFontFreeType@UniText@@UEAA@XZ
	??1UniObject@UniText@@UEAA@XZ
	??1UniTextGenerator@UniText@@QEAA@XZ
	??1UniTextureNative@UniText@@UEAA@XZ
	??1VertexAttribute@Graphics@@QEAA@XZ
	??1WindowsFontFallbackProvider@UniText@@UEAA@XZ
	??1_Creator@_Dir_enum_impl@filesystem@std@@QEAA@XZ
	??1_Find_file_handle@filesystem@std@@QEAA@XZ
	??1_Reallocation_guard@?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@QEAA@XZ
	??1_Reallocation_guard@?$vector@UGlyphLookUpTable@UniText@@V?$allocator@UGlyphLookUpTable@UniText@@@std@@@std@@QEAA@XZ
	??1_Reallocation_guard@?$vector@URichTextTag@UniTextGenerator@UniText@@V?$allocator@URichTextTag@UniTextGenerator@UniText@@@std@@@std@@QEAA@XZ
	??1_Reallocation_guard@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ
	??1_Ref_count_base@std@@UEAA@XZ
	??1_System_error_message@std@@QEAA@XZ
	??1_Vaporization_guard@?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@QEAA@XZ
	??1any@std@@QEAA@XZ
	??1bad_array_new_length@std@@UEAA@XZ
	??1bad_optional_access@std@@UEAA@XZ
	??1bad_variant_access@std@@UEAA@XZ
	??1directory_entry@filesystem@std@@QEAA@XZ
	??1directory_iterator@filesystem@std@@QEAA@XZ
	??1error_category@std@@UEAA@XZ
	??1exception@std@@UEAA@XZ
	??1filesystem_error@filesystem@std@@UEAA@XZ
	??1memory_resource@pmr@std@@UEAA@XZ
	??1path@filesystem@std@@QEAA@XZ
	??1recursive_directory_iterator@filesystem@std@@QEAA@XZ
	??1system_error@std@@UEAA@XZ
	??1unsynchronized_pool_resource@pmr@std@@UEAA@XZ
	??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV01@$$QEAV01@@Z
	??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV01@AEBV01@@Z
	??4?$unordered_map@V?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@U?$hash@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@U?$equal_to@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@2@@std@@QEAAAEAV01@$$QEAV01@@Z
	??4?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@std@@QEAAAEAV01@$$QEAV01@@Z
	??4?$vector@W4ScriptType@UniText@@V?$allocator@W4ScriptType@UniText@@@std@@@std@@QEAAAEAV01@V?$initializer_list@W4ScriptType@UniText@@@1@@Z
	??A?$unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@2@@std@@QEAAAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@AEBV21@@Z
	??A?$unordered_map@W4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@U?$hash@W4ScriptType@UniText@@@4@U?$equal_to@W4ScriptType@UniText@@@4@V?$allocator@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@@4@@std@@QEAAAEAV?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@1@$$QEAW4ScriptType@UniText@@@Z
	??Edirectory_iterator@filesystem@std@@QEAAAEAV012@XZ
	??R<lambda_dc688a12290ca8f9f3692d6885975fee>@@QEBAHPEBUFT_Vector_@@00PEAX@Z
	??_0path@filesystem@std@@QEAAAEAV012@AEBV012@@Z
	??_7?$LocalArenaMemoryResource@$0CA@@UniText@@6B@
	??_7?$LocalArenaMemoryResource@$0IA@@UniText@@6B@
	??_7?$MaxRectsGlyphPacker@$0A@@UniText@@6B@
	??_7?$_Ref_count_obj2@U_Dir_enum_impl@filesystem@std@@@std@@6B@
	??_7?$_Ref_count_obj2@U_Recursive_dir_enum_impl@filesystem@std@@@std@@6B@
	??_7AndroidFontFallbackProvider@UniText@@6B@
	??_7AppleFontFallbackProvider@UniText@@6B@
	??_7DefaultFontFallbackProvider@UniText@@6B@
	??_7GridGlyphPacker@UniText@@6B@
	??_7IFontFallbackProvider@UniText@@6B@
	??_7ITextProcessor@UniText@@6B@
	??_7ITexture@UniText@@6B@
	??_7IUniFontImpl@UniText@@6B@
	??_7IUniGlyphPacker@UniText@@6B@
	??_7IUniPostProcessor@UniText@@6B@
	??_7SDFGenerator@UniText@@6B@
	??_7ShelfGlyphPacker@UniText@@6B@
	??_7SimpleTextProcessor@UniText@@6B@
	??_7UniCustomFont@UniText@@6B@
	??_7UniFont@UniText@@6B@
	??_7UniFontFreeType@UniText@@6B@
	??_7UniObject@UniText@@6B@
	??_7UniTextureNative@UniText@@6B@
	??_7VSDFGenerator@UniText@@6B@
	??_7WindowsFontFallbackProvider@UniText@@6B@
	??_7WriteToFileGenerator@UniText@@6B@
	??_7_Generic_error_category@std@@6B@
	??_7_Identity_equal_resource@pmr@std@@6B@
	??_7_System_error@std@@6B@
	??_7_System_error_category@std@@6B@
	??_7bad_alloc@std@@6B@
	??_7bad_array_new_length@std@@6B@
	??_7bad_optional_access@std@@6B@
	??_7bad_variant_access@std@@6B@
	??_7exception@std@@6B@
	??_7filesystem_error@filesystem@std@@6B@
	??_7runtime_error@std@@6B@
	??_7system_error@std@@6B@
	??_7unsynchronized_pool_resource@pmr@std@@6B@
	?AddEmptyQuad@UniTextGenerator@UniText@@AEAAXAEBU?$Vector2@M@2@0AEBUColor@2@@Z
	?AddFallbackFont@UniFont@UniText@@QEAAXPEBD@Z
	?AddGlyphQuad@UniTextGenerator@UniText@@AEAAXAEBU?$Vector2@M@2@MAEBUColor@2@AEBUGlyphInfo@2@PEBUSizedPage@2@@Z
	?AddLineQuad@UniTextGenerator@UniText@@AEAAXAEBU?$Vector2@M@2@0MAEBUColor@2@@Z
	?AddRef@UniObject@UniText@@QEAAXXZ
	?AnalyzeTextComplexity@UnifiedTextProcessor@UniText@@SA?AW4TextComplexity@2@AEBV?$vector@GV?$polymorphic_allocator@G@pmr@std@@@std@@_N@Z
	?AppendText@UniTextGenerator@UniText@@QEAAXPEBD@Z
	?ApplyAlignment@UniTextGenerator@UniText@@AEAAXXZ
	?ApplyFontAtlases@@YAXXZ
	?ApplyOverflow@UniTextGenerator@UniText@@AEAAXH@Z
	?ApplyRTLReordering@SimpleTextProcessor@UniText@@AEAAXAEBUTextProcessingParams@2@@Z
	?ApplyRTLReorderingDirect@SimpleTextProcessor@UniText@@AEAAXPEAVUniTextGenerator@2@@Z
	?ApplySettings@UniTextGlobal@UniText@@SAXXZ
	?AutoDetectDirection@UnifiedTextProcessor@UniText@@SA?AW4ProcessingDirection@2@AEBV?$vector@GV?$polymorphic_allocator@G@pmr@std@@@std@@@Z
	?BestAreaFit@?$MaxRectsGlyphPacker@$0A@@UniText@@AEAA_NPEAU?$Rect@G@2@AEAH1@Z
	?BestLongSideFit@?$MaxRectsGlyphPacker@$0A@@UniText@@AEAA_NPEAU?$Rect@G@2@AEAH1@Z
	?BestShortSideFit@?$MaxRectsGlyphPacker@$0A@@UniText@@AEAA_NPEAU?$Rect@G@2@AEAH1@Z
	?BottomLeftRule@?$MaxRectsGlyphPacker@$0A@@UniText@@AEAA_NPEAU?$Rect@G@2@AEAH1@Z
	?BuildWordBoundaries@UniTextGenerator@UniText@@AEAAXXZ
	?CacheGlyph@UniFont@UniText@@AEAAXGHAEAUGlyphInfo@2@@Z
	?CanHandle@SimpleTextProcessor@UniText@@UEBA_NW4TextComplexity@2@@Z
	?CheckFontSizeFits@UniTextGenerator@UniText@@AEAA_NF@Z
	?Clear@?$MaxRectsGlyphPacker@$0A@@UniText@@UEAAXXZ
	?Clear@GridGlyphPacker@UniText@@UEAAXXZ
	?Clear@ShelfGlyphPacker@UniText@@UEAAXXZ
	?Clear@TextureEntry@UniText@@QEAAXXZ
	?Clear@UniFontAtlasEntry@UniText@@QEAAXXZ
	?ClearData@TextureEntry@UniText@@QEAAXAEBU?$Rect@G@2@@Z
	?ClearGlyphs@UniFont@UniText@@QEAAXXZ
	?ConicTo@Sdf_FillPass@UniText@@QEAAXU?$Vector2@M@2@0@Z
	?ConicTo@Sdf_LinePass@UniText@@QEAAXU?$Vector2@M@2@0@Z
	?ContactPointRule@?$MaxRectsGlyphPacker@$0A@@UniText@@AEAA_NPEAU?$Rect@G@2@AEAH1@Z
	?ConvertUTF16toUTF32@@YA?AW4ConversionResult@@PEAPEBGPEBGPEAPEAIPEAIW4ConversionFlags@@@Z
	?ConvertUTF16toUTF8@@YA?AW4ConversionResult@@PEAPEBGPEBGPEAPEAEPEAEW4ConversionFlags@@@Z
	?ConvertUTF32toUTF16@@YA?AW4ConversionResult@@PEAPEBIPEBIPEAPEAGPEAGW4ConversionFlags@@@Z
	?ConvertUTF32toUTF8@@YA?AW4ConversionResult@@PEAPEBIPEBIPEAPEAEPEAEW4ConversionFlags@@@Z
	?ConvertUTF8toUTF16@@YA?AW4ConversionResult@@PEAPEBEPEBEPEAPEAGPEAGW4ConversionFlags@@@Z
	?ConvertUTF8toUTF16@@YA_NPEBDHPEAGAEAH@Z
	?ConvertUTF8toUTF32@@YA?AW4ConversionResult@@PEAPEBEPEBEPEAPEAIPEAIW4ConversionFlags@@@Z
	?Create@UniTextureNative@UniText@@UEAAXGGW4Format@ITexture@2@@Z
	?CreateFace@UniCustomFont@UniText@@UEAA_NPEBD@Z
	?CreateFace@UniFont@UniText@@AEAA_NPEBD@Z
	?CreateFace@UniFontFreeType@UniText@@UEAA_NPEBD@Z
	?CreateFromFile@UniFont@UniText@@SA?AV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@PEBD@Z
	?CreateFromMemory@UniFont@UniText@@SA?AV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@PEBDPEBEK@Z
	?CreateGlyphPacker@UniText@@YA?AV?$unique_ptr@VIUniGlyphPacker@UniText@@U?$default_delete@VIUniGlyphPacker@UniText@@@std@@@std@@W4PackingMethod@1@@Z
	?CreateMemoryFace@UniCustomFont@UniText@@UEAA_NPEBEK@Z
	?CreateMemoryFace@UniFont@UniText@@AEAA_NPEBEK@Z
	?CreateMemoryFace@UniFontFreeType@UniText@@UEAA_NPEBEK@Z
	?CreatePostProcessor@UniText@@YA?AV?$unique_ptr@VIUniPostProcessor@UniText@@U?$default_delete@VIUniPostProcessor@UniText@@@std@@@std@@W4RenderMode@1@@Z
	?Destroy@UniTextGlobal@UniText@@SAXXZ
	?DestroySystemFonts@UniFont@UniText@@SAXXZ
	?DetectScriptType@UniFontFallbackCache@UniText@@QEAA?AW4ScriptType@2@G@Z
	?Ensure@EDTAA_Grid@UniText@@QEAAXHH@Z
	?EnumFontFamiliesExProc@@YAHPEBUtagLOGFONTA@@PEBUtagTEXTMETRICA@@K_J@Z
	?EnumerateSystemFonts@AndroidFontFallbackProvider@UniText@@AEAAXXZ
	?EnumerateSystemFonts@AppleFontFallbackProvider@UniText@@AEAAXXZ
	?EnumerateSystemFonts@WindowsFontFallbackProvider@UniText@@AEAAXXZ
	?FindFamilyForFont@UniFontFallbackCache@UniText@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@Z
	?FindGlyphInfo@GlyphLookUpTable@UniText@@QEAA_NGGPEAPEAUGlyphInfo@2@@Z
	?FindNewEntry@UniAtlasCache@UniText@@QEAA_NAEAUTextureEntry@2@@Z
	?FindWordBoundaryBefore@UniTextGenerator@UniText@@AEAAHH@Z
	?FreeAll@UniAtlasCache@UniText@@QEAAXXZ
	?FreeUnusedEntry@UniAtlasCache@UniText@@QEAAXAEAUTextureEntry@2@@Z
	?FreeUnusedPages@UniFontAtlasEntry@UniText@@QEAAXXZ
	?FromConic@SdfCurveSpace@UniText@@SA?AU12@AEBU?$Vector2@M@2@00@Z
	?FromLine@SdfCurveSpace@UniText@@SA?AU12@AEBU?$Vector2@M@2@0@Z
	?GenerateFromString@UniGUID@UniText@@SAHPEBD@Z
	?GetAllFamilyNames@UniFontFallbackCache@UniText@@QEBAXAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@Z
	?GetAscender@UniCustomFont@UniText@@UEBAMH@Z
	?GetAscender@UniFont@UniText@@QEBAMH@Z
	?GetAscender@UniFontFreeType@UniText@@UEBAMH@Z
	?GetAtlasCache@UniTextGlobal@UniText@@SAPEAVUniAtlasCache@2@XZ
	?GetAtlasPacker@UniFont@UniText@@QEBAPEBVIUniGlyphPacker@2@HH@Z
	?GetBestMatchingFontFromFamily@UniFontFallbackCache@UniText@@QEBAHAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4FontStyle@2@@Z
	?GetCharIndex@UniCustomFont@UniText@@UEAAIGI@Z
	?GetCharIndex@UniFont@UniText@@QEBAIGI@Z
	?GetCharIndex@UniFontFreeType@UniText@@UEAAIGI@Z
	?GetDefaultFallbackFont@AndroidFontFallbackProvider@UniText@@UEAA?AUFontFallbackInfo@2@W4ScriptType@2@@Z
	?GetDefaultFallbackFont@AppleFontFallbackProvider@UniText@@UEAA?AUFontFallbackInfo@2@W4ScriptType@2@@Z
	?GetDefaultFallbackFont@DefaultFontFallbackProvider@UniText@@UEAA?AUFontFallbackInfo@2@W4ScriptType@2@@Z
	?GetDefaultFallbackFont@UniFontFallbackCache@UniText@@QEAA?AUFontFallbackInfo@2@W4ScriptType@2@@Z
	?GetDefaultFallbackFont@WindowsFontFallbackProvider@UniText@@UEAA?AUFontFallbackInfo@2@W4ScriptType@2@@Z
	?GetDescender@UniCustomFont@UniText@@UEBAMH@Z
	?GetDescender@UniFont@UniText@@QEBAMH@Z
	?GetDescender@UniFontFreeType@UniText@@UEBAMH@Z
	?GetFallbackFonts@AndroidFontFallbackProvider@UniText@@UEAA?AV?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@W4ScriptType@2@@Z
	?GetFallbackFonts@AppleFontFallbackProvider@UniText@@UEAA?AV?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@W4ScriptType@2@@Z
	?GetFallbackFonts@DefaultFontFallbackProvider@UniText@@UEAA?AV?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@W4ScriptType@2@@Z
	?GetFallbackFonts@UniFontFallbackCache@UniText@@QEAAXW4ScriptType@2@AEAV?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@Z
	?GetFallbackFonts@WindowsFontFallbackProvider@UniText@@UEAA?AV?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@W4ScriptType@2@@Z
	?GetFamilyFonts@UniFontFallbackCache@UniText@@QEBAAEBV?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@@Z
	?GetFamilyName@UniCustomFont@UniText@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	?GetFamilyName@UniFont@UniText@@QEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	?GetFamilyName@UniFontFreeType@UniText@@UEBAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	?GetFont@UniFontCache@UniText@@QEAAPEAVUniFont@2@H@Z
	?GetFontAtlas@UniFont@UniText@@QEBAPEBVITexture@2@HH@Z
	?GetFontCache@UniTextGlobal@UniText@@SAPEAVUniFontCache@2@XZ
	?GetFontFallbackCache@UniTextGlobal@UniText@@SAPEAVUniFontFallbackCache@2@XZ
	?GetFontForGlyphInFallbacks@UniFont@UniText@@AEAA_NAEBUGlyphLoadParam@2@PEAPEAV12@@Z
	?GetFontFromFamily@UniFontFallbackCache@UniText@@QEBAHAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4FontStyle@2@@Z
	?GetFontGUID@UniFont@UniText@@QEBAHXZ
	?GetFontStyle@UniCustomFont@UniText@@UEBA?AW4FontStyle@2@XZ
	?GetFontStyle@UniFont@UniText@@QEBA?AW4FontStyle@2@XZ
	?GetFontStyle@UniFontFreeType@UniText@@UEBA?AW4FontStyle@2@XZ
	?GetFormat@UniTextureNative@UniText@@UEBA?AW4Format@ITexture@2@XZ
	?GetGUID@UniObject@UniText@@QEBAHXZ
	?GetGlyphLookUpTable@UniFontAtlasEntry@UniText@@QEAAPEAUGlyphLookUpTable@2@H@Z
	?GetGlyphPage@UniFont@UniText@@QEBAPEBUSizedPage@2@HAEBUGlyphInfo@2@@Z
	?GetGlyphUV@UniTextGenerator@UniText@@AEAA?AU?$Vector4@M@2@G@Z
	?GetHeight@UniTextureNative@UniText@@UEBAGXZ
	?GetImplForRendering@UniFont@UniText@@AEAAPEAVIUniFontImpl@2@AEBUGlyphLoadParam@2@@Z
	?GetKerning@UniCustomFont@UniText@@UEBAMGG@Z
	?GetKerning@UniFont@UniText@@QEBAMGG@Z
	?GetKerning@UniFontFreeType@UniText@@UEBAMGG@Z
	?GetKerning@UniTextGenerator@UniText@@QEBAMGG@Z
	?GetLineSpacing@UniCustomFont@UniText@@UEBAMH@Z
	?GetLineSpacing@UniFont@UniText@@QEBAMH@Z
	?GetLineSpacing@UniFontFreeType@UniText@@UEBAMH@Z
	?GetLoadFlags@UniText@@YAHW4RenderMode@1@@Z
	?GetMaxFontSize@UniFont@UniText@@QEBAHXZ
	?GetMinFontSize@UniFont@UniText@@QEBAHXZ
	?GetMutableSettings@UniTextGlobal@UniText@@SAAEAVSettings@12@XZ
	?GetName@UniObject@UniText@@QEBAPEBDXZ
	?GetPadding@UniFont@UniText@@QEBAEXZ
	?GetRefCount@UniObject@UniText@@QEBAHXZ
	?GetRenderMode@UniFont@UniText@@QEBA?AW4RenderMode@2@XZ
	?GetReplacementChar@UniFont@UniText@@QEBAIXZ
	?GetSettings@UniTextGlobal@UniText@@SAAEBVSettings@12@XZ
	?GetSystemFontDirectory@AndroidFontFallbackProvider@UniText@@UEAAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	?GetSystemFontDirectory@AppleFontFallbackProvider@UniText@@UEAAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	?GetSystemFontDirectory@DefaultFontFallbackProvider@UniText@@UEAAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	?GetSystemFontDirectory@UniFontFallbackCache@UniText@@QEAAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	?GetSystemFontDirectory@WindowsFontFallbackProvider@UniText@@UEAAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	?GetSystemFontPath@AndroidFontFallbackProvider@UniText@@UEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBV34@@Z
	?GetSystemFontPath@AppleFontFallbackProvider@UniText@@UEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBV34@@Z
	?GetSystemFontPath@DefaultFontFallbackProvider@UniText@@UEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBV34@@Z
	?GetSystemFontPath@UniFontFallbackCache@UniText@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBV34@@Z
	?GetSystemFontPath@WindowsFontFallbackProvider@UniText@@UEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBV34@@Z
	?GetTextOffset@UniTextGenerator@UniText@@AEAAXAEAU?$Vector2@M@2@@Z
	?GetTexture@TextureEntry@UniText@@QEBAPEAVITexture@2@XZ
	?GetTextureData@UniTextureNative@UniText@@UEBAPEBXXZ
	?GetTextureHandle@UniTextureNative@UniText@@UEBAPEAXXZ
	?GetWidth@UniTextureNative@UniText@@UEBAGXZ
	?HasFreeSpaces@?$MaxRectsGlyphPacker@$0A@@UniText@@UEAA_NXZ
	?HasFreeSpaces@GridGlyphPacker@UniText@@UEAA_NXZ
	?HasFreeSpaces@ShelfGlyphPacker@UniText@@UEAA_NXZ
	?HashString@UniGUID@UniText@@CAHPEBD@Z
	?InitLibrary@UniCustomFont@UniText@@UEAA_NXZ
	?InitLibrary@UniFont@UniText@@AEAA_NXZ
	?InitLibrary@UniFontFreeType@UniText@@UEAA_NXZ
	?Initialize@?$MaxRectsGlyphPacker@$0A@@UniText@@UEAAXU?$Rect@G@2@EE_N@Z
	?Initialize@AndroidFontFallbackProvider@UniText@@UEAA_NXZ
	?Initialize@AppleFontFallbackProvider@UniText@@UEAA_NXZ
	?Initialize@DefaultFontFallbackProvider@UniText@@UEAA_NXZ
	?Initialize@FreeTypeContext@UniText@@QEAA_NXZ
	?Initialize@GridGlyphPacker@UniText@@UEAAXU?$Rect@G@2@EE_N@Z
	?Initialize@ShelfGlyphPacker@UniText@@UEAAXU?$Rect@G@2@EE_N@Z
	?Initialize@UniFontFallbackCache@UniText@@QEAA_NXZ
	?Initialize@UniTextGlobal@UniText@@SA_NXZ
	?Initialize@WindowsFontFallbackProvider@UniText@@UEAA_NXZ
	?InitializePenWithOffset@UniTextGenerator@UniText@@QEAAXXZ
	?InitializeSystemFonts@UniFont@UniText@@SAXXZ
	?InsertCharacter@SimpleTextProcessor@UniText@@AEAA_NGAEBUTextProcessingParams@2@H@Z
	?InsertCharacter@UniTextGenerator@UniText@@AEAA_NG@Z
	?InsertNewLine@SimpleTextProcessor@UniText@@AEAA_NAEBUTextProcessingParams@2@@Z
	?InsertNewLine@UniTextGenerator@UniText@@AEAA_NXZ
	?InsertSpace@SimpleTextProcessor@UniText@@AEAA_NGAEBUTextProcessingParams@2@H@Z
	?InsertSpace@UniTextGenerator@UniText@@AEAA_NGH@Z
	?IsComplexScript@UnifiedTextProcessor@UniText@@CA_NG@Z
	?IsFontInstalled@AndroidFontFallbackProvider@UniText@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?IsFontInstalled@AppleFontFallbackProvider@UniText@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?IsFontInstalled@DefaultFontFallbackProvider@UniText@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?IsFontInstalled@UniFontFallbackCache@UniText@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?IsFontInstalled@WindowsFontFallbackProvider@UniText@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?IsRTLCharacter@UnifiedTextProcessor@UniText@@CA_NG@Z
	?IsValid@UniTextureNative@UniText@@UEAA?B_NXZ
	?IterateRichTextTags@UniTextGenerator@UniText@@AEAAXH@Z
	?LineTo@Sdf_FillPass@UniText@@QEAAXU?$Vector2@M@2@@Z
	?LineTo@Sdf_LinePass@UniText@@QEAAXU?$Vector2@M@2@@Z
	?LoadFontFromMemory@UniFontCache@UniText@@QEAAPEAVUniFont@2@PEBDPEBEK@Z
	?LoadGlyph@UniCustomFont@UniText@@UEAA_NIHW4RenderMode@2@PEAPEAUGlyphInfo@2@I@Z
	?LoadGlyph@UniFontFreeType@UniText@@UEAA_NIHW4RenderMode@2@PEAPEAUGlyphInfo@2@I@Z
	?MarkTextsAsUnused@UniTextGenerator@UniText@@AEAAXXZ
	?OffsetQuad@UniTextGenerator@UniText@@AEAAXHHAEBU?$Vector2@M@2@@Z
	?OnFontSizeChanged@UniFontAtlasEntry@UniText@@QEAAXHH@Z
	?OnInitialized@UniFontFreeType@UniText@@AEAAXXZ
	?Pack@?$MaxRectsGlyphPacker@$0A@@UniText@@UEAA_NPEAU?$Rect@G@2@@Z
	?Pack@GridGlyphPacker@UniText@@UEAA_NPEAU?$Rect@G@2@@Z
	?Pack@ShelfGlyphPacker@UniText@@UEAA_NPEAU?$Rect@G@2@@Z
	?ParseRichTextTag@@YA_NV?$basic_string_view@GU?$char_traits@G@std@@@std@@HAEAHAEAURichTextTagParseResult@@@Z
	?PreProcessTexts@UniTextGenerator@UniText@@AEAAXPEBGH@Z
	?PreRenderGlyph@UniFT_Renderer@UniText@@QEAA_NHW4RenderMode@2@PEAUGlyphInfo@2@@Z
	?PreRenderGlyph@UniSDF_Renderer@UniText@@QEAA_NHW4RenderMode@2@PEAUGlyphInfo@2@@Z
	?PreloadSystemFallbackFonts@UniFontFallbackCache@UniText@@AEAAXXZ
	?Process@SDFGenerator@UniText@@UEAA?BUImageData@2@PEBU32@E@Z
	?Process@VSDFGenerator@UniText@@UEAA?BUImageData@2@PEBU32@E@Z
	?Process@WriteToFileGenerator@UniText@@UEAA?BUImageData@2@PEBU32@E@Z
	?ProcessAutoSizing@UniTextGenerator@UniText@@AEAA_NXZ
	?ProcessCharactersLogical@SimpleTextProcessor@UniText@@AEAA_NAEBUTextProcessingParams@2@@Z
	?ProcessLeftToRight@SimpleTextProcessor@UniText@@AEAA_NAEBUTextProcessingParams@2@@Z
	?ProcessOverflow@UniTextGenerator@UniText@@AEAAXXZ
	?ProcessRightToLeft@SimpleTextProcessor@UniText@@AEAA_NAEBUTextProcessingParams@2@@Z
	?ProcessText@SimpleTextProcessor@UniText@@UEAA_NAEBUTextProcessingParams@2@@Z
	?ProcessText@UnifiedTextProcessor@UniText@@QEAA_NAEBUTextProcessingParams@2@@Z
	?ProcessTextUnified@UniTextGenerator@UniText@@AEAA_NXZ
	?ProcessWrapping@UniTextGenerator@UniText@@AEAAXXZ
	?Process_EDTAA@SDFGenerator@UniText@@AEAA?BUImageData@2@PEBU32@E@Z
	?Process_EDTAA@VSDFGenerator@UniText@@AEAA?BUImageData@2@PEBU32@E@Z
	?Rebuild@UniTextGenerator@UniText@@QEAAXXZ
	?Rebuild@UniTextGenerator@UniText@@QEAAX_N@Z
	?RegisterCSharpCallback@@YAXP6APEAXPEAX@Z@Z
	?RegisterDefaultFallbacks@AndroidFontFallbackProvider@UniText@@AEAAXXZ
	?RegisterDefaultFallbacks@AppleFontFallbackProvider@UniText@@AEAAXXZ
	?RegisterDefaultFallbacks@DefaultFontFallbackProvider@UniText@@AEAAXXZ
	?RegisterDefaultFallbacks@WindowsFontFallbackProvider@UniText@@AEAAXXZ
	?RegisterFontToFamily@UniFontFallbackCache@UniText@@QEAAXAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@W4FontStyle@2@H@Z
	?Release@FreeTypeContext@UniText@@QEAAXXZ
	?Release@UniObject@UniText@@QEAAXXZ
	?Release@UniTextureNative@UniText@@UEAAXXZ
	?ReleaseFTFace@UniFontFreeType@UniText@@AEAAXXZ
	?RemoveFamily@UniFontFallbackCache@UniText@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	?Render@SdfPipeline@UniText@@QEAAXXZ
	?RenderGlyph@UniCustomFont@UniText@@UEAA_N$$QEAUGlyphRenderData@2@I@Z
	?RenderGlyph@UniCustomFont@UniText@@UEAA_NIHW4RenderMode@2@PEAPEAUGlyphInfo@2@PEAPEAEI@Z
	?RenderGlyph@UniFT_Renderer@UniText@@QEAA_NAEBUGlyphRenderData@2@@Z
	?RenderGlyph@UniFontFreeType@UniText@@UEAA_N$$QEAUGlyphRenderData@2@I@Z
	?RenderGlyph@UniFontFreeType@UniText@@UEAA_NIHW4RenderMode@2@PEAPEAUGlyphInfo@2@PEAPEAEI@Z
	?RenderGlyph@UniSDF_Renderer@UniText@@QEAA_NAEBUGlyphRenderData@2@@Z
	?RenderLineDecorations@UniTextGenerator@UniText@@AEAAXXZ
	?RequiresShaping@UnifiedTextProcessor@UniText@@CA_NG@Z
	?ResetPenHorizontally@UniTextGenerator@UniText@@QEAAXXZ
	?SearchForUnusedNode@?$IndexedQuadTree@$06@UniText@@QEAAFH@Z
	?SelectProcessor@UnifiedTextProcessor@UniText@@AEAAPEAVITextProcessor@2@W4TextComplexity@2@@Z
	?SetActive@UniTextGenerator@UniText@@QEAAX_N@Z
	?SetAutoSizeRange@UniTextGenerator@UniText@@QEAAXFF@Z
	?SetCharacterSpacing@UniTextGenerator@UniText@@QEAAXM@Z
	?SetDirty@UniTextGenerator@UniText@@AEAAXW4DirtyType@12@_N@Z
	?SetDirty@UniTextGenerator@UniText@@AEAAXXZ
	?SetExtents@UniTextGenerator@UniText@@QEAAXMM@Z
	?SetFont@UniTextGenerator@UniText@@QEAAXPEAVUniFont@2@@Z
	?SetFont@UniTextGenerator@UniText@@QEAAXPEBD@Z
	?SetFontSize@UniTextGenerator@UniText@@QEAAXF@Z
	?SetFontSizeForRendering@UniCustomFont@UniText@@UEAAXHW4RenderMode@2@@Z
	?SetFontSizeForRendering@UniFont@UniText@@QEAAXH@Z
	?SetFontSizeForRendering@UniFontFreeType@UniText@@UEAAXHW4RenderMode@2@@Z
	?SetFontSizeRange@UniFont@UniText@@QEAAXHH@Z
	?SetFontStyle@UniTextGenerator@UniText@@QEAAXW4FontStyle@2@@Z
	?SetHorizontalAlignment@UniTextGenerator@UniText@@QEAAXW4TextAlignment@2@@Z
	?SetLineSpacing@UniTextGenerator@UniText@@QEAAXM@Z
	?SetLogHandler@UniText@@YAXP6AXHPEBDZZ@Z
	?SetPackingMethod@UniFont@UniText@@QEAAXW4PackingMethod@2@@Z
	?SetPadding@UniFont@UniText@@QEAAXE@Z
	?SetPivot@UniTextGenerator@UniText@@QEAAXU?$Vector2@M@2@@Z
	?SetRenderMode@UniFont@UniText@@QEAAXW4RenderMode@2@@Z
	?SetReplacementChar@UniFont@UniText@@QEAAXI@Z
	?SetStrokeSize@UniTextGenerator@UniText@@QEAAXM@Z
	?SetStyle@UniTextGenerator@UniText@@QEAAXXZ
	?SetText@UniTextGenerator@UniText@@QEAAXPEBD@Z
	?SetVerticalAlignment@UniTextGenerator@UniText@@QEAAXW4TextAlignment@2@@Z
	?Setup@UniFont@UniText@@QEAAXAEBUFontProperties@2@@Z
	?SetupResources@SdfPipeline@UniText@@AEAAXXZ
	?ShouldBreakWord@UniTextGenerator@UniText@@AEAA?B_NG@Z
	?Split@?$MaxRectsGlyphPacker@$0A@@UniText@@AEAA_NPEAU?$Rect@G@2@0@Z
	?TryPackGlyph@UniFont@UniText@@AEAAPEAUTextureEntry@2@GHAEAUGlyphInfo@2@@Z
	?TryPackGlyph@UniFont@UniText@@AEAA_NGHPEBEAEAUGlyphInfo@2@AEAF@Z
	?UniTextLog@UniText@@YAXHPEBDZZ
	?UnloadGlyph@UniFont@UniText@@QEAAXGH@Z
	?UnloadUnusedGlyphs@UniFont@UniText@@QEAAHXZ
	?Unpack@?$MaxRectsGlyphPacker@$0A@@UniText@@UEAAXAEBU?$Rect@G@2@@Z
	?Unpack@GridGlyphPacker@UniText@@UEAAXAEBU?$Rect@G@2@@Z
	?Unpack@ShelfGlyphPacker@UniText@@UEAAXAEBU?$Rect@G@2@@Z
	?UpdateAllFontsIfSettingsChanged@UniFont@UniText@@SAXXZ
	?Visit@?$MaxRectsGlyphPacker@$0A@@UniText@@UEBAXV?$function@$$A6AXAEBU?$Rect@G@UniText@@_N@Z@std@@@Z
	?Visit@GridGlyphPacker@UniText@@UEBAXV?$function@$$A6AXAEBU?$Rect@G@UniText@@_N@Z@std@@@Z
	?Visit@ShelfGlyphPacker@UniText@@UEBAXV?$function@$$A6AXAEBU?$Rect@G@UniText@@_N@Z@std@@@Z
	?WrapLine@UniTextGenerator@UniText@@AEAAXH@Z
	?WriteData@TextureEntry@UniText@@QEAAXAEBU?$Rect@G@2@PEBE@Z
	?WriteUInt8@UniTextureNative@UniText@@UEAAXAEBU?$Rect@G@2@E@Z
	?WriteUInt8@UniTextureNative@UniText@@UEAAXAEBU?$Rect@G@2@PEBE@Z
	?_Advance_and_reset_if_no_more_files@_Dir_enum_impl@filesystem@std@@SA?AW4__std_win_error@@AEAV?$shared_ptr@U_Dir_enum_impl@filesystem@std@@@3@@Z
	?_Advance_and_reset_if_no_more_files@_Recursive_dir_enum_impl@filesystem@std@@SA?AW4__std_win_error@@AEAV?$shared_ptr@U_Recursive_dir_enum_impl@filesystem@std@@@3@@Z
	?_Allocate@_Pool@unsynchronized_pool_resource@pmr@std@@QEAAPEAXAEAU234@@Z
	?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@U_Iterator_base0@2@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
	?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@@std@@@std@@@2@@Z
	?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@@std@@@std@@@2@@Z
	?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@std@@@std@@@2@@Z
	?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@std@@@std@@@2@@Z
	?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@std@@@std@@@2@@Z
	?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@@std@@@std@@@2@@Z
	?_Buy_nonzero@?$vector@W4ScriptType@UniText@@V?$allocator@W4ScriptType@UniText@@@std@@@std@@AEAAX_K@Z
	?_Buy_raw@?$vector@HV?$allocator@H@std@@@std@@AEAAX_K@Z
	?_Buy_raw@?$vector@USdfVertex@UniText@@V?$allocator@USdfVertex@UniText@@@std@@@std@@AEAAX_K@Z
	?_Buy_raw@?$vector@W4ScriptType@UniText@@V?$allocator@W4ScriptType@UniText@@@std@@@std@@AEAAX_K@Z
	?_Change_array@?$vector@EV?$allocator@E@std@@@std@@AEAAXQEAE_K1@Z
	?_Change_array@?$vector@HV?$allocator@H@std@@@std@@AEAAXQEAH_K1@Z
	?_Change_array@?$vector@MV?$allocator@M@std@@@std@@AEAAXQEAM_K1@Z
	?_Change_array@?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@AEAAXQEAU?$Vector2@G@UniText@@_K1@Z
	?_Change_array@?$vector@U?$Vector2@M@UniText@@V?$allocator@U?$Vector2@M@UniText@@@std@@@std@@AEAAXQEAU?$Vector2@M@UniText@@_K1@Z
	?_Change_array@?$vector@U?$Vector4@M@UniText@@V?$allocator@U?$Vector4@M@UniText@@@std@@@std@@AEAAXQEAU?$Vector4@M@UniText@@_K1@Z
	?_Change_array@?$vector@UColor@UniText@@V?$allocator@UColor@UniText@@@std@@@std@@AEAAXQEAUColor@UniText@@_K1@Z
	?_Change_array@?$vector@ULayer@ShelfGlyphPacker@UniText@@V?$polymorphic_allocator@ULayer@ShelfGlyphPacker@UniText@@@pmr@std@@@std@@AEAAXQEAULayer@ShelfGlyphPacker@UniText@@_K1@Z
	?_Change_array@?$vector@ULine@UniTextGenerator@UniText@@V?$polymorphic_allocator@ULine@UniTextGenerator@UniText@@@pmr@std@@@std@@AEAAXQEAULine@UniTextGenerator@UniText@@_K1@Z
	?_Change_array@?$vector@USdfVertex@UniText@@V?$allocator@USdfVertex@UniText@@@std@@@std@@AEAAXQEAUSdfVertex@UniText@@_K1@Z
	?_Change_array@?$vector@U_Pool@unsynchronized_pool_resource@pmr@std@@V?$polymorphic_allocator@U_Pool@unsynchronized_pool_resource@pmr@std@@@34@@std@@AEAAXQEAU_Pool@unsynchronized_pool_resource@pmr@2@_K1@Z
	?_Change_array@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXQEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@_K1@Z
	?_Deallocate_for_capacity@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CAXAEAV?$allocator@D@2@QEAD_K@Z
	?_Delete_this@?$_Ref_count_obj2@U_Dir_enum_impl@filesystem@std@@@std@@EEAAXXZ
	?_Delete_this@?$_Ref_count_obj2@U_Recursive_dir_enum_impl@filesystem@std@@@std@@EEAAXXZ
	?_Destroy@?$_Ref_count_obj2@U_Dir_enum_impl@filesystem@std@@@std@@EEAAXXZ
	?_Destroy@?$_Ref_count_obj2@U_Recursive_dir_enum_impl@filesystem@std@@@std@@EEAAXXZ
	?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
	?_Find_root_name_end@filesystem@std@@YAPEB_WQEB_W0@Z
	?_Forced_rehash@?$_Hash@V?$_Umap_traits@GUGlyphInfo@UniText@@V?$_Uhash_compare@GU?$hash@G@std@@U?$equal_to@G@2@@std@@V?$allocator@U?$pair@$$CBGUGlyphInfo@UniText@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z
	?_Forced_rehash@?$_Hash@V?$_Umap_traits@HV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@V?$_Uhash_compare@HU?$hash@H@std@@U?$equal_to@H@2@@2@V?$allocator@U?$pair@$$CBHV?$unique_ptr@VUniFont@UniText@@U?$default_delete@VUniFont@UniText@@@std@@@std@@@std@@@2@$0A@@std@@@std@@IEAAX_K@Z
	?_Forced_rehash@?$_Hash@V?$_Umap_traits@HV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@V?$_Uhash_compare@HU?$hash@H@std@@U?$equal_to@H@2@@2@V?$allocator@U?$pair@$$CBHV?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@@std@@@2@$0A@@std@@@std@@IEAAX_K@Z
	?_Forced_rehash@?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@HV?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H@std@@@2@$0A@@std@@@std@@IEAAX_K@Z
	?_Forced_rehash@?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V12@@std@@@2@$0A@@std@@@std@@IEAAX_K@Z
	?_Forced_rehash@?$_Hash@V?$_Umap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$map@W4FontStyle@UniText@@HU?$less@W4FontStyle@UniText@@@std@@V?$allocator@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@4@@2@@std@@@2@$0A@@std@@@std@@IEAAX_K@Z
	?_Forced_rehash@?$_Hash@V?$_Umap_traits@V?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@V?$_Uhash_compare@V?$basic_string_view@GU?$char_traits@G@std@@@std@@U?$hash@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@U?$equal_to@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@2@$0A@@std@@@std@@IEAAX_K@Z
	?_Forced_rehash@?$_Hash@V?$_Umap_traits@W4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@V?$_Uhash_compare@W4ScriptType@UniText@@U?$hash@W4ScriptType@UniText@@@std@@U?$equal_to@W4ScriptType@UniText@@@4@@4@V?$allocator@U?$pair@$$CBW4ScriptType@UniText@@V?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@@std@@@4@$0A@@std@@@std@@IEAAX_K@Z
	?_Forced_rehash@?$_Hash@V?$_Uset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@IEAAX_K@Z
	?_Get_any_status@directory_entry@filesystem@std@@AEBA?AU_File_status_and_error@23@W4__std_fs_stats_flags@@@Z
	?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z
	?_Growmap@?$deque@HV?$allocator@H@std@@@std@@AEAAX_K@Z
	?_Growmap@?$deque@USdf_RenderData@UniText@@V?$allocator@USdf_RenderData@UniText@@@std@@@std@@AEAAX_K@Z
	?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@H@std@@@std@@QEAAPEAU?$_Tree_node@HPEAX@2@U?$_Tree_id@PEAU?$_Tree_node@HPEAX@std@@@2@QEAU32@@Z
	?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@std@@@2@QEAU32@@Z
	?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBW4FontStyle@UniText@@H@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBW4FontStyle@UniText@@H@std@@PEAX@std@@@2@QEAU32@@Z
	?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@QEAXV?$RefCounterPtr@VITexture2D@Graphics@@@Graphics@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@QEAXV?$RefCounterPtr@VITexture2D@Graphics@@@Graphics@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@QEAXV?$RefCounterPtr@VITexture2D@Graphics@@@Graphics@@@std@@PEAX@std@@@2@QEAU32@@Z
	?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@2@@Z
	?_Make_ec@std@@YA?AVerror_code@1@W4__std_win_error@@@Z
	?_Maklocwcs@std@@YAPEA_WPEB_W@Z
	?_Move_assign_unequal_alloc@?$vector@URange@ShelfGlyphPacker@UniText@@V?$polymorphic_allocator@URange@ShelfGlyphPacker@UniText@@@pmr@std@@@std@@AEAAXAEAV12@@Z
	?_Open_dir@_Dir_enum_impl@filesystem@std@@SA?AW4__std_win_error@@AEAVpath@23@W4directory_options@23@AEAU_Find_file_handle@23@AEAU__std_fs_find_data@@@Z
	?_Pretty_message@filesystem_error@filesystem@std@@CA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@V?$basic_string_view@DU?$char_traits@D@std@@@3@AEBVpath@23@1@Z
	?_Range_compare@filesystem@std@@YAHQEB_W000@Z
	?_Refresh@_Dir_enum_impl@filesystem@std@@QEAAXAEBU__std_fs_find_data@@@Z
	?_Rehash_for_1@?$_Hash@V?$_Umap_traits@V?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@V?$_Uhash_compare@V?$basic_string_view@GU?$char_traits@G@std@@@std@@U?$hash@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@U?$equal_to@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@2@$0A@@std@@@std@@IEAAXXZ
	?_Remove_filename_and_separator@path@filesystem@std@@QEAAXXZ
	?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBUGlyphLookUpKey@UniText@@UGlyphInfo@2@@std@@PEAX@2@@Z
	?_Skip_dots@_Dir_enum_impl@filesystem@std@@SA?AW4__std_win_error@@W4__std_fs_dir_handle@@AEAU__std_fs_find_data@@@Z
	?_Throw_bad_array_new_length@std@@YAXXZ
	?_Throw_bad_optional_access@std@@YAXXZ
	?_Throw_bad_variant_access@std@@YAXXZ
	?_Throw_fs_error@filesystem@std@@YAXPEBDAEBVerror_code@2@AEBVpath@12@@Z
	?_Throw_fs_error@filesystem@std@@YAXPEBDW4__std_win_error@@@Z
	?_Throw_fs_error@filesystem@std@@YAXPEBDW4__std_win_error@@AEBVpath@12@@Z
	?_Throw_system_error@std@@YAXW4errc@1@@Z
	?_Throw_system_error_from_std_win_error@std@@YAXW4__std_win_error@@@Z
	?_Throw_tree_length_error@std@@YAXXZ
	?_Tidy@?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@AEAAXXZ
	?_Tidy@?$vector@U_Pool@unsynchronized_pool_resource@pmr@std@@V?$polymorphic_allocator@U_Pool@unsynchronized_pool_resource@pmr@std@@@34@@std@@AEAAXXZ
	?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ
	?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
	?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
	?_Unchecked_erase@?$_Hash@V?$_Umap_traits@V?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@V?$_Uhash_compare@V?$basic_string_view@GU?$char_traits@G@std@@@std@@U?$hash@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@U?$equal_to@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@2@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@PEAX@2@PEAU32@QEAU32@@Z
	?_Xlen@?$deque@HV?$allocator@H@std@@@std@@CAXXZ
	?_Xlen@?$deque@USdf_RenderData@UniText@@V?$allocator@USdf_RenderData@UniText@@@std@@@std@@CAXXZ
	?_Xlen@?$vector@_NV?$allocator@_N@std@@@std@@SAXXZ
	?_Xlen_string@std@@YAXXZ
	?_Xlength@?$vector@EV?$allocator@E@std@@@std@@CAXXZ
	?_Xlength@?$vector@GV?$polymorphic_allocator@G@pmr@std@@@std@@CAXXZ
	?_Xlength@?$vector@HV?$allocator@H@std@@@std@@CAXXZ
	?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ
	?_Xlength@?$vector@MV?$allocator@M@std@@@std@@CAXXZ
	?_Xlength@?$vector@PEAVUniFont@UniText@@V?$allocator@PEAVUniFont@UniText@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@U?$IndexedQuadTree@$06@UniText@@V?$allocator@U?$IndexedQuadTree@$06@UniText@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@U?$Rect@G@UniText@@V?$allocator@U?$Rect@G@UniText@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@U?$Vector2@G@UniText@@V?$allocator@U?$Vector2@G@UniText@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@U?$Vector2@M@UniText@@V?$allocator@U?$Vector2@M@UniText@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@U?$Vector4@M@UniText@@V?$allocator@U?$Vector4@M@UniText@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@UColor@UniText@@V?$allocator@UColor@UniText@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@UGlyphLookUpTable@UniText@@V?$allocator@UGlyphLookUpTable@UniText@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@ULayer@ShelfGlyphPacker@UniText@@V?$polymorphic_allocator@ULayer@ShelfGlyphPacker@UniText@@@pmr@std@@@std@@CAXXZ
	?_Xlength@?$vector@ULetter@UniTextGenerator@UniText@@V?$polymorphic_allocator@ULetter@UniTextGenerator@UniText@@@pmr@std@@@std@@CAXXZ
	?_Xlength@?$vector@ULine@UniTextGenerator@UniText@@V?$polymorphic_allocator@ULine@UniTextGenerator@UniText@@@pmr@std@@@std@@CAXXZ
	?_Xlength@?$vector@ULineDecoration@UniTextGenerator@UniText@@V?$polymorphic_allocator@ULineDecoration@UniTextGenerator@UniText@@@pmr@std@@@std@@CAXXZ
	?_Xlength@?$vector@URange@ShelfGlyphPacker@UniText@@V?$polymorphic_allocator@URange@ShelfGlyphPacker@UniText@@@pmr@std@@@std@@CAXXZ
	?_Xlength@?$vector@URichTextTag@UniTextGenerator@UniText@@V?$allocator@URichTextTag@UniTextGenerator@UniText@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@USdfVertex@UniText@@V?$allocator@USdfVertex@UniText@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@USizedPage@UniText@@V?$polymorphic_allocator@USizedPage@UniText@@@pmr@std@@@std@@CAXXZ
	?_Xlength@?$vector@UWord@UniTextGenerator@UniText@@V?$polymorphic_allocator@UWord@UniTextGenerator@UniText@@@pmr@std@@@std@@CAXXZ
	?_Xlength@?$vector@U_Find_file_handle@filesystem@std@@V?$allocator@U_Find_file_handle@filesystem@std@@@3@@std@@CAXXZ
	?_Xlength@?$vector@U_Pool@unsynchronized_pool_resource@pmr@std@@V?$polymorphic_allocator@U_Pool@unsynchronized_pool_resource@pmr@std@@@34@@std@@CAXXZ
	?_Xlength@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@CAXXZ
	?_Xlength@?$vector@V?$unique_ptr@VITexture@UniText@@U?$default_delete@VITexture@UniText@@@std@@@std@@V?$allocator@V?$unique_ptr@VITexture@UniText@@U?$default_delete@VITexture@UniText@@@std@@@std@@@2@@std@@CAXXZ
	?_Xlength@?$vector@W4FontStyle@UniText@@V?$allocator@W4FontStyle@UniText@@@std@@@std@@CAXXZ
	?_Xlength@?$vector@W4ScriptType@UniText@@V?$allocator@W4ScriptType@UniText@@@std@@@std@@CAXXZ
	?_Xran@?$_String_val@U?$_Simple_types@_W@std@@@std@@SAXXZ
	?_Xran@?$basic_string_view@GU?$char_traits@G@std@@@std@@CAXXZ
	?allocate@?$allocator@H@std@@QEAAPEAH_K@Z
	?allocate@UTF16String@UniText@@QEAAX_K@Z
	?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z
	?append@?$basic_string@DU?$char_traits@D@std@@V?$polymorphic_allocator@D@pmr@2@@std@@QEAAAEAV12@QEBD_K@Z
	?append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z
	?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z
	?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z
	?begin@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA?AV?$_String_iterator@V?$_String_val@U?$_Simple_types@D@std@@@std@@@2@XZ
	?begin@filesystem@std@@YA?AVdirectory_iterator@12@V312@@Z
	?bsdf_is_edge@@YA_NAEAUEDTAA_Grid@UniText@@HHH@Z
	?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ
	?clear@?$_Hash@V?$_Umap_traits@V?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@V?$_Uhash_compare@V?$basic_string_view@GU?$char_traits@G@std@@@std@@U?$hash@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@U?$equal_to@V?$basic_string_view@GU?$char_traits@G@std@@@std@@@2@@2@V?$allocator@U?$pair@$$CBV?$basic_string_view@GU?$char_traits@G@std@@@std@@V?$variant@HMV?$basic_string_view@GU?$char_traits@G@std@@@std@@UColor@UniText@@W4FontStyle@4@@2@@std@@@2@$0A@@std@@@std@@QEAAXXZ
	?clear@?$_Tree@V?$_Tset_traits@HU?$less@H@std@@V?$allocator@H@2@$0A@@std@@@std@@QEAAXXZ
	?compute_edge_distance@@YA?AU?$Vector2@M@UniText@@AEAUEDTAA_Grid@2@HHHH@Z
	?deallocate@?$allocator@W4ScriptType@UniText@@@std@@QEAAXQEAW4ScriptType@UniText@@_K@Z
	?default_error_condition@_System_error_category@std@@UEBA?AVerror_condition@2@H@Z
	?default_error_condition@error_category@std@@UEBA?AVerror_condition@2@H@Z
	?do_allocate@?$LocalArenaMemoryResource@$0CA@@UniText@@EEAAPEAX_K0@Z
	?do_allocate@?$LocalArenaMemoryResource@$0IA@@UniText@@EEAAPEAX_K0@Z
	?do_allocate@unsynchronized_pool_resource@pmr@std@@MEAAPEAX_K_K@Z
	?do_deallocate@?$LocalArenaMemoryResource@$0CA@@UniText@@EEAAXPEAX_K1@Z
	?do_deallocate@?$LocalArenaMemoryResource@$0IA@@UniText@@EEAAXPEAX_K1@Z
	?do_deallocate@unsynchronized_pool_resource@pmr@std@@MEAAXQEAX_K1@Z
	?do_is_equal@_Identity_equal_resource@pmr@std@@MEBA_NAEBVmemory_resource@23@@Z
	?end@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA?AV?$_String_iterator@V?$_String_val@U?$_Simple_types@D@std@@@std@@@2@XZ
	?end@filesystem@std@@YA?AVdirectory_iterator@12@V312@@Z
	?equivalent@error_category@std@@UEBA_NAEBVerror_code@2@H@Z
	?equivalent@error_category@std@@UEBA_NHAEBVerror_condition@2@@Z
	?extension@path@filesystem@std@@QEBA?AV123@XZ
	?filename@path@filesystem@std@@QEBA?AV123@XZ
	?first_pass@@YAXAEAUEDTAA_Grid@UniText@@HH@Z
	?init_iterator@@YAXH@Z
	?insert@?$_Hash@V?$_Uset_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$_Uhash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@$0A@@std@@@std@@QEAA?AU?$pair@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@@std@@_N@2@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z
	?isLegalUTF8Sequence@@YAEPEBE0@Z
	?is_absolute@path@filesystem@std@@QEBA_NXZ
	?is_regular_file@directory_entry@filesystem@std@@QEBA_NXZ
	?make_error_code@std@@YA?AVerror_code@1@W4errc@1@@Z
	?message@_Generic_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@H@Z
	?message@_System_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@H@Z
	?name@_Generic_error_category@std@@UEBAPEBDXZ
	?name@_System_error_category@std@@UEBAPEBDXZ
	?push@?$queue@HV?$deque@HV?$allocator@H@std@@@std@@@std@@QEAAX$$QEAH@Z
	?push_back@?$vector@UFontFallbackInfo@UniText@@V?$allocator@UFontFallbackInfo@UniText@@@std@@@std@@QEAAXAEBUFontFallbackInfo@UniText@@@Z
	?push_back@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAAX$$QEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z
	?release@unsynchronized_pool_resource@pmr@std@@QEAAXXZ
	?reserve@?$vector@EV?$allocator@E@std@@@std@@QEAAX_K@Z
	?resize@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAX_KD@Z
	?resize@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAX_K_W@Z
	?second_pass@@YAXAEAUEDTAA_Grid@UniText@@HH@Z
	?string@path@filesystem@std@@QEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@XZ
	?what@bad_optional_access@std@@UEBAPEBDXZ
	?what@bad_variant_access@std@@UEBAPEBDXZ
	?what@exception@std@@UEBAPEBDXZ
	?what@filesystem_error@filesystem@std@@UEBAPEBDXZ
	AddFallbackFont
	AppendText
	ClearFontAtlases
	ClearGlyphs
	CreateNativeFont
	CreateUniText
	DestroyLibrary
	DestroyNativeFont
	DestroyUniText
	EnableAutoSize
	EnableKerning
	EnableRTL
	EnableRichText
	FillVertices
	FillVerticesPerChannel
	GetAutoSizeRange
	GetCharacterSpacing
	GetColor
	GetFamilyName
	GetFont
	GetFontGUID
	GetFontSize
	GetFontSizeRange
	GetFontSizeStep
	GetFontStyle
	GetHorizontalAlignment
	GetHorizontalOverflow
	GetLineSpacing
	GetMaxTextureCount
	GetMaxTextureSize
	GetMinTextureEntrySize
	GetPivot
	GetStrokeSize
	GetStyle
	GetTextureCount
	GetTextureHandle
	GetVertexCount
	GetVerticalAlignment
	GetVerticalOverflow
	HasAutoSize
	HasKerning
	HasRTL
	HasRichText
	InitializeLibrary
	LoadFontFromMemory
	LoadFontFromPath
	Rebuild
	RegisterCreateTextureCallback
	ResetToDefaults
	SetActive
	SetAutoSizeRange
	SetCharacterSpacing
	SetColor
	SetExtents
	SetFont
	SetFontSize
	SetFontSizeRange
	SetFontSizeStep
	SetFontStyle
	SetHorizontalAlignment
	SetHorizontalOverflow
	SetLineSpacing
	SetMaxTextureCount
	SetMaxTextureSize
	SetMinTextureEntrySize
	SetPackingMothod
	SetPadding
	SetPivot
	SetRenderMode
	SetStrokeSize
	SetText
	SetVerticalAlignment
	SetVerticalOverflow
	TryPackGlyphs
	UnloadUnusedGlyphs
	UpdateAllFontsIfSettingsChanged
	__local_stdio_printf_options
	__std_is_file_not_found
	printf
