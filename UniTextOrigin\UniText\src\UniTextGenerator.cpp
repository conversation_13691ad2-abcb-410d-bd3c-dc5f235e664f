#include "UniTextGenerator.h"
#include "UniTextGlobal.h"
#include "RenderAPI.h"
#include "Common/UniMacro.h"

#include <algorithm>

NAMESPACE_USE

// complete the definition of s_MemPool in "Utility/TextUtility.h";
SharedMemPool<uint16> UTF16String::s_MemPool{};

// Define predefined attribute keys
const UniString16 UniTextGenerator::PredefinedKeys::x = u"x";
const UniString16 UniTextGenerator::PredefinedKeys::y = u"y";
const UniString16 UniTextGenerator::PredefinedKeys::width = u"width";
const UniString16 UniTextGenerator::PredefinedKeys::height = u"height";
const UniString16 UniTextGenerator::PredefinedKeys::value = u"value";

// Overload pattern with std::visit
// More details at: https://www.modernescpp.com/index.php/visiting-a-std-variant-with-the-overload-pattern
template<typename ... Ts>
struct Overload : Ts ... {
    using Ts::operator() ...;
};
template<class... Ts> Overload(Ts...)->Overload<Ts...>;

// Not used in Unity engine
#ifndef UNITY
/////////////////////////////////////////////////////////////////
/// Rebuilds
/////////////////////////////////////////////////////////////////
static std::vector<UniTextGenerator*> s_dirtyTexts;
// TODO: collect and free unused
void UniTextGenerator::RebuildUniTexts()
{
    if (s_dirtyTexts.size() > 0)
    {
        for (int i = 0; i < s_dirtyTexts.size(); i++)
        {
            // do rebuild;
            s_dirtyTexts[i]->Rebuild();
        }
        s_dirtyTexts.clear();

        // Fonts UnloadUnusedGlyphs();
        auto fontCache = UniTextGlobal::GetFontCache();
        fontCache->ForeachFont([](UniFont* font)
            {
                font->UnloadUnusedGlyphs();
            });

        //AtlasManager::GetInstance()->ApplyDirtyTextures();
    }
}
#endif

#if USE_ROUND_POSITION
#include <cmath>
#define ROUND(x) std::round(x)
#else
#define ROUND(x) x
#endif

/////////////////////////////////////////////////////////////////
/// UniTextGenerator
/////////////////////////////////////////////////////////////////
UniTextGenerator::UniTextGenerator()
    :
    m_IsActive(true),
    m_IsDirty(false),
    m_Features(3),              // 1 | 2 = 3 enable richText��kerning by default
    m_DirtyFlags(0),
    m_HorizontalOverflow(TextOverflow::Truncate),
    m_VerticalOverflow(TextOverflow::Overflow),
    m_HorizontalAlignment(TextAlignment::Left),
    m_VerticalAlignment(TextAlignment::Left),
    m_BaseFontSize(28),
    m_LastFontSize(28),
    m_AutoSizeMinFontSize(14),
    //m_AutoSizeMaxFontSize(72),
    m_BaseColor(Color::white),
    m_BaseStrokeSize(0.0f),
    m_BaseLineSpacing(0.0f),
    m_BaseCharSpacing(0.0f),
    m_Extents(200, 100),
    m_Pivot(0.5f, 0.5f),
    m_Font(nullptr),
    m_LastUsedFont(nullptr),
    m_FontStyle(FontStyle::Regular),
    m_Text(&m_LocalMemResForString),
    m_Unicodes(&m_LocalMemResForVector),
    m_UnifiedProcessor(std::make_unique<UnifiedTextProcessor>())

{

}

UniTextGenerator::~UniTextGenerator()
{
    //DELETE_PTR(m_Text);
    m_Font = nullptr;
}

#pragma region RichText tags
static const std::array<std::string, 23> kBuiltInColorKey
{
    "red",
    "cyan",
    "blue",
    "darkblue",
    "lightblue",
    "purple",
    "yellow",
    "lime",
    "fuchsia",
    "white",
    "silver",
    "grey",
    "black",
    "orange",
    "brown",
    "maroon",
    "green",
    "olive",
    "navy",
    "teal",
    "aqua",
    "magenta",
    "transparent"
};

static const std::array<Color, 23> kBuiltInColorValue
{
    0xff0000ff,
    0x00ffffff,
    0x0000ffff,
    0x0000a0ff,
    0xadd8e6ff,
    0x800080ff,
    0xffff00ff,
    0x00ff00ff,
    0xff00ffff,
    0xffffffff,
    0xc0c0c0ff,
    0x808080ff,
    0x000000ff,
    0xffa500ff,
    0xa52a2aff,
    0x800000ff,
    0x008000ff,
    0x808000ff,
    0x000080ff,
    0x008080ff,
    0x00ffffff,
    0xff00ffff,
    0x00000000,
};

/// <summary>
/// Strongly related to UniGlyphData.h: RichTextTagType
/// </summary>
static const std::array<std::string, (int)(RichTextTagType::Max)> kBuiltInTagName
{
    "color","style","size","stroke","u","d","quad","material","nobr"
};

// ignore case
static inline auto Comparer = [](const UniStringView& a, const std::string_view& b)
{
    return std::equal(a.begin(), a.end(),
        b.begin(), b.end(),
        [](uint16 c_a, char c_b) {
            return tolower(c_a) == tolower(static_cast<uint16>(c_b));
        });
};

static RichTextTagType TagName2Type(const UniStringView tagName)
{
    for (int i = 0; i < kBuiltInTagName.size(); i++)
    {
        if (Comparer(tagName, kBuiltInTagName[i]))
            return (RichTextTagType)i;
    }

    return RichTextTagType::Unknown;
}

// for local method,
static inline bool IsHex(uint16 c)
{
    return (c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F');
}

static void HexStringToColor(UniStringView str, size_t bytes, Color& data)
{
    for (size_t i = 0; i < bytes; i++)
    {
        uint8 b;
        char ch = str[2 * i + 0];
        if (ch <= '9')
            b = (ch - '0') << 4;
        else if (ch <= 'Z')
            b = (ch - 'A' + 10) << 4;
        else
            b = (ch - 'a' + 10) << 4;

        ch = str[2 * i + 1];
        if (ch <= '9')
            b |= (ch - '0');
        else if (ch <= 'Z')
            b |= (ch - 'A' + 10);
        else
            b |= (ch - 'a' + 10);

        data[i] = b;
    }
}

static int StringToInt(UniStringView str)
{
    int val = 0;
    for (int i = 0; i < str.size(); i++)
    {
        if (str[i] >= '0' && str[i] <= '9')
        {
            val = val * 10 + (str[i] - '0');
        }
    }
    return val;
}

// Helper function to validate names (both tag names and attribute names) - unified validation
static inline bool IsValidName(UniStringView name)
{
    if (name.empty() || name.size() > 32) return false; // Reasonable length limit

    // Names should start with a letter or underscore (for attributes)
    uint16 firstChar = name[0];
    if (!std::isalpha(firstChar) && firstChar != '_') return false;

    // Rest can be letters, digits, underscores, or hyphens (for attributes)
    for (size_t i = 1; i < name.size(); i++)
    {
        uint16 c = name[i];
        if (!std::isalnum(c) && c != '_' && c != '-') return false;
    }

    return true;
}

// Helper function to parse UniStringView to appropriate type with validation (zero-copy)
static inline UniTextGenerator::AttributeValue ParseAttributeValue(UniStringView value)
{
    if (value.empty()) return value;

    // Try to parse as number first
    if (value.size() > 0 && (std::isdigit(value[0]) || value[0] == '-' || value[0] == '+'))
    {
        // Check if it contains a decimal point for float
        bool hasDecimal = false;
        for (auto c : value)
        {
            if (c == '.')
            {
                hasDecimal = true;
                break;
            }
        }

        if (hasDecimal)
        {
            // Parse as float using existing StringToFloat-like logic
            float result = 0.0f;
            float decimal = 0.1f;
            bool afterDecimal = false;
            bool negative = false;

            for (size_t i = 0; i < value.size(); i++)
            {
                uint16 c = value[i];
                if (i == 0 && c == '-')
                {
                    negative = true;
                }
                else if (c == '.')
                {
                    afterDecimal = true;
                }
                else if (c >= '0' && c <= '9')
                {
                    if (afterDecimal)
                    {
                        result += (c - '0') * decimal;
                        decimal *= 0.1f;
                    }
                    else
                    {
                        result = result * 10.0f + (c - '0');
                    }
                }
            }

            return negative ? -result : result;
        }
        else
        {
            // Parse as integer using existing StringToInt logic
            int result = 0;
            bool negative = false;

            for (size_t i = 0; i < value.size(); i++)
            {
                uint16 c = value[i];
                if (i == 0 && c == '-')
                {
                    negative = true;
                }
                else if (c >= '0' && c <= '9')
                {
                    result = result * 10 + (c - '0');
                }
            }

            return negative ? -result : result;
        }
    }

    // Return as UniStringView (zero-copy)
    return value;
}

// Helper functions for color and style parsing
static inline bool ParseColorValue(UniStringView value, Color& outColor)
{
    if (value.empty()) return false;

    if (value[0] == '#')
    {
        if (value.size() <= 9)
        {
            for (size_t i = 1; i < value.size(); i++)
            {
                if (!IsHex(value[i])) return false;
            }

            if (value.size() == 7 || value.size() == 9)
            {
                // Extract hex part (skip the '#')
                auto hexView = value.substr(1);
                HexStringToColor(hexView, hexView.size() / 2, outColor);
                return true;
            }
        }
    }
    else
    {
        // Check built-in color names
        for (size_t i = 0; i < kBuiltInColorKey.size(); i++)
        {
            if (Comparer(value, kBuiltInColorKey[i]))
            {
                outColor = kBuiltInColorValue[i];
                return true;
            }
        }
    }

    return false;
}

static inline bool ParseStyleValue(UniStringView value, FontStyle& outStyle)
{
    if (Comparer(value, "italic"))
    {
        outStyle = FontStyle::Italic;
        return true;
    }
    else if (Comparer(value, "bold"))
    {
        outStyle = FontStyle::Bold;
        return true;
    }

    return false;
}

// Enhanced function to parse attributes into the unified system
static inline void ParseAttributes(UniTextGenerator::RichTextTag& tag, RichTextTagType tagType)
{
    const auto& attributes = tag.attributes;

    switch (tagType)
    {
    case RichTextTagType::Color:
        {
            // Look for explicit color attribute first, then use first attribute as color value
            UniStringView colorValue;

            // Check for explicit "color" attribute
            for (const auto& [key, value] : attributes)
            {
                if (Comparer(key, "color"))
                {
                    if (std::holds_alternative<UniStringView>(value))
                    {
                        colorValue = std::get<UniStringView>(value);
                        break;
                    }
                }
            }

            // If no explicit color attribute, use first attribute for backward compatibility
            if (colorValue.empty() && !attributes.empty())
            {
                auto firstAttr = attributes.begin();
                if (std::holds_alternative<UniStringView>(firstAttr->second))
                {
                    colorValue = std::get<UniStringView>(firstAttr->second);
                }
            }

            if (!colorValue.empty())
            {
                Color parsedColor;
                if (ParseColorValue(colorValue, parsedColor))
                {
                    // Store as unified "value" attribute
                    tag.SetValue(parsedColor);
                }
            }
        }
        break;

    case RichTextTagType::Style:
        {
            // Look for explicit style attribute first, then use first attribute
            UniStringView styleValue;

            for (const auto& [key, value] : attributes)
            {
                if (Comparer(key, "style"))
                {
                    if (std::holds_alternative<UniStringView>(value))
                    {
                        styleValue = std::get<UniStringView>(value);
                        break;
                    }
                }
            }

            // If no explicit style attribute, use first attribute for backward compatibility
            if (styleValue.empty() && !attributes.empty())
            {
                auto firstAttr = attributes.begin();
                if (std::holds_alternative<UniStringView>(firstAttr->second))
                {
                    styleValue = std::get<UniStringView>(firstAttr->second);
                }
            }

            if (!styleValue.empty())
            {
                FontStyle style = FontStyle::Regular;
                if (ParseStyleValue(styleValue, style))
                {
                    // Store as unified "value" attribute
                    tag.SetValue(style);
                }
            }
        }
        break;

    case RichTextTagType::Size:
        {
            // Look for explicit size attribute first, then use first attribute
            for (const auto& [key, value] : attributes)
            {
                if (Comparer(key, "size"))
                {
                    if (std::holds_alternative<int>(value))
                    {
                        tag.SetValue(std::get<int>(value));
                        return;
                    }
                    else if (std::holds_alternative<UniStringView>(value))
                    {
                        auto sizeView = std::get<UniStringView>(value);
                        int size = StringToInt(sizeView);
                        tag.SetValue(size);
                        return;
                    }
                }
            }

            // If no explicit size attribute, use first attribute for backward compatibility
            if (!attributes.empty())
            {
                auto firstAttr = attributes.begin();
                if (std::holds_alternative<int>(firstAttr->second))
                {
                    tag.SetValue(std::get<int>(firstAttr->second));
                }
                else if (std::holds_alternative<UniStringView>(firstAttr->second))
                {
                    auto sizeView = std::get<UniStringView>(firstAttr->second);
                    int size = StringToInt(sizeView);
                    tag.SetValue(size);
                }
            }
        }
        break;

    case RichTextTagType::Quad:
        {
            // Quad tags use multiple attributes directly - no single "value"
            // All attributes (x, y, width, height, etc.) are already stored in tag.attributes
        }
        break;

    default:break;
    }
}

// Enhanced parsing utility functions for cleaner code
namespace ParseUtils {

    // Skip characters while condition is true
    template<typename Predicate>
    static inline size_t SkipWhile(const UniStringView& str, size_t pos, Predicate pred)
    {
        while (pos < str.size() && pred(str[pos]))
        {
            pos++;
        }
        return pos;
    }

    // Skip characters until condition is true (while condition is false)
    template<typename Predicate>
    static inline size_t SkipUntil(const UniStringView& str, size_t pos, Predicate pred)
    {
        while (pos < str.size() && !pred(str[pos]))
        {
            pos++;
        }
        return pos;
    }

    // Skip whitespace characters (space and tab)
    static inline size_t SkipWhitespace(const UniStringView& str, size_t pos)
    {
        return SkipWhile(str, pos, [](uint16 c) { return c == ' ' || c == '\t'; });
    }

    // Skip until any of the specified characters is found
    static inline size_t SkipUntilAny(const UniStringView& str, size_t pos, std::initializer_list<uint16> chars)
    {
        return SkipUntil(str, pos, [chars](uint16 c) {
            for (auto ch : chars) {
                if (c == ch) return true;
            }
            return false;
        });
    }

    // Skip while character is any of the specified characters
    static inline size_t SkipWhileAny(const UniStringView& str, size_t pos, std::initializer_list<uint16> chars)
    {
        return SkipWhile(str, pos, [chars](uint16 c) {
            for (auto ch : chars) {
                if (c == ch) return true;
            }
            return false;
        });
    }

    // Skip until specific character
    static inline size_t SkipUntilChar(const UniStringView& str, size_t pos, uint16 targetChar)
    {
        return SkipUntil(str, pos, [targetChar](uint16 c) { return c == targetChar; });
    }

    // Skip while character is not any of the specified characters
    static inline size_t SkipWhileNotAny(const UniStringView& str, size_t pos, std::initializer_list<uint16> chars)
    {
        return SkipWhile(str, pos, [chars](uint16 c) {
            for (auto ch : chars) {
                if (c == ch) return false;
            }
            return true;
        });
    }
}

// Helper function to trim whitespace from UniStringView
static inline UniStringView TrimWhitespace(const UniStringView& str)
{
    if (str.empty()) return str;

    size_t start = 0;
    size_t end = str.size();

    // Trim leading whitespace
    while (start < end && (str[start] == ' ' || str[start] == '\t' || str[start] == '\n' || str[start] == '\r'))
    {
        start++;
    }

    // Trim trailing whitespace
    while (end > start && (str[end - 1] == ' ' || str[end - 1] == '\t' || str[end - 1] == '\n' || str[end - 1] == '\r'))
    {
        end--;
    }

    return str.substr(start, end - start);
}

struct RichTextTagParseResult
{
    RichTextTagType tagType { RichTextTagType::Unknown };
    std::optional<UniTextGenerator::RichTextTag> tag { std::nullopt };
};

// Enhanced tag parser that handles multiple attributes
// Uses RichTextTagParseResult as in-out parameter for better structure
bool ParseRichTextTag(const UniStringView str, int startIndex, int& endIndex, RichTextTagParseResult& outResult)
{
    // Clear the result first
    outResult.tagType = RichTextTagType::Unknown;
    outResult.tag = UniTextGenerator::RichTextTag{}; // Initialize with empty tag instead of nullopt

    auto& tag = outResult.tag.value();

    // Find the end of the tag
    endIndex = ParseUtils::SkipUntilChar(str, endIndex, '>');

    if (endIndex >= str.size())
    {
        LOG_DEBUG("ParseRichTextTag: Tag not properly closed, reached end of string");
        return false;
    }

    // Get the tag content (everything between < and >)
    auto tagContent = str.substr(startIndex, endIndex - startIndex);

    // Check for self-closing tag
    if (tagContent.size() > 0 && tagContent[tagContent.size() - 1] == '/')
    {
        tag.isSelfClosing = true;
        tagContent = tagContent.substr(0, tagContent.size() - 1);
    }

    // Parse tag name and attributes
    size_t pos = 0;

    // Skip leading whitespace
    pos = ParseUtils::SkipWhitespace(tagContent, pos);

    if (pos >= tagContent.size())
    {
        return false;
    }

    // Extract tag name
    size_t tagNameStart = pos;
    pos = ParseUtils::SkipUntilAny(tagContent, pos, {' ', '\t', '='});

    if (pos == tagNameStart)
    {
        LOG_DEBUG("ParseRichTextTag: No tag name found");
        return false;
    }

    auto tagNameView = tagContent.substr(tagNameStart, pos - tagNameStart);
    // Validate tag name directly with UniStringView
    if (!IsValidName(tagNameView))
    {
        return false;
    }

    // Parse attributes (with reasonable limit to prevent DoS)
    const size_t MAX_ATTRIBUTES = 16;
    size_t attributeCount = 0;

    // Special case: handle tags like <color=#FFFF00> where there's no explicit attribute name
    // Skip whitespace after tag name
    pos = ParseUtils::SkipWhitespace(tagContent, pos);

    // Check if we have an immediate '=' after tag name (like <color=#FFFF00>)
    if (pos < tagContent.size() && tagContent[pos] == '=')
    {
        pos++; // Skip '='

        // Skip whitespace after =
        pos = ParseUtils::SkipWhitespace(tagContent, pos);

        if (pos < tagContent.size())
        {
            // Extract the value
            size_t attrValueStart = pos;

            // Handle quoted values
            if (tagContent[pos] == '"' || tagContent[pos] == '\'')
            {
                uint16 quote = tagContent[pos];
                pos++; // Skip opening quote
                attrValueStart = pos;

                pos = ParseUtils::SkipUntilChar(tagContent, pos, quote);

                if (pos < tagContent.size())
                {
                    auto attrValueView = tagContent.substr(attrValueStart, pos - attrValueStart);
                    tag.SetValue(ParseAttributeValue(attrValueView));
                    attributeCount++;
                    pos++; // Skip closing quote;
                }
            }
            else
            {
                // Unquoted value - read until space or end
                pos = ParseUtils::SkipWhileNotAny(tagContent, pos, {' ', '\t'});

                auto attrValueView = TrimWhitespace(tagContent.substr(attrValueStart, pos - attrValueStart));
                tag.SetValue(ParseAttributeValue(attrValueView));
                attributeCount++;
            }
        }
    }

    // Continue with normal attribute parsing for any remaining attributes
    while (pos < tagContent.size() && attributeCount < MAX_ATTRIBUTES)
    {
        // Skip whitespace
        pos = ParseUtils::SkipWhitespace(tagContent, pos);

        if (pos >= tagContent.size()) break;

        // Extract attribute name
        size_t attrNameStart = pos;
        pos = ParseUtils::SkipUntilAny(tagContent, pos, {' ', '\t', '='});

        if (pos == attrNameStart) break;

        auto attrNameView = TrimWhitespace(tagContent.substr(attrNameStart, pos - attrNameStart));

        // Validate attribute name directly with UniStringView
        if (!IsValidName(attrNameView))
        {
            LOG_DEBUG("ParseRichTextTag: Invalid attribute name, skipping");
            // Skip invalid attribute and continue parsing
            continue;
        }

        // Skip whitespace around =
        pos = ParseUtils::SkipWhitespace(tagContent, pos);

        if (pos >= tagContent.size() || tagContent[pos] != '=')
        {
            // Attribute without value (like in HTML)
            tag.attributes[attrNameView] = UniStringView();
            attributeCount++;
            LOG_DEBUG("ParseRichTextTag: Found attribute without value");
            continue;
        }

        pos++; // Skip '='

        // Skip whitespace after =
        pos = ParseUtils::SkipWhitespace(tagContent, pos);

        if (pos >= tagContent.size()) break;

        // Extract attribute value
        size_t attrValueStart = pos;

        // Handle quoted values
        if (tagContent[pos] == '"' || tagContent[pos] == '\'')
        {
            uint16 quote = tagContent[pos];
            pos++; // Skip opening quote
            attrValueStart = pos;

            pos = ParseUtils::SkipUntilChar(tagContent, pos, quote);

            if (pos < tagContent.size())
            {
                auto attrValueView = tagContent.substr(attrValueStart, pos - attrValueStart);
                tag.attributes[attrNameView] = ParseAttributeValue(attrValueView);
                attributeCount++;
                pos++; // Skip closing quote
            }
        }
        else
        {
            // Unquoted value - read until space or end
            pos = ParseUtils::SkipWhileNotAny(tagContent, pos, {' ', '\t'});

            auto attrValueView = TrimWhitespace(tagContent.substr(attrValueStart, pos - attrValueStart));
            tag.attributes[attrNameView] = ParseAttributeValue(attrValueView);
            attributeCount++;
        }
    }

    tag.isValid = true;
    // Set the result
    outResult.tagType = TagName2Type(tagNameView);

    return true;
}

void UniTextGenerator::PreProcessTexts(const uint16* unicodes, int length)
{
    MarkTextsAsUnused();

    m_Unicodes.clear();

    if (HasRichText())
    {
        // clear formats
        for (auto& stack : m_FormatStack)
        {
            stack.clear();
        }

        UniStringView str(unicodes, (size_t)length);
        int actualIndex = 0;
        int cur = 0;
        while (cur < str.size())
        {
            auto c = str[cur];
            switch (c)
            {
            case '<':
            {
                auto tagStartIndex = cur + 1;
                auto tagEndIndex = tagStartIndex;

                if (tagEndIndex < str.size() && str[tagEndIndex] == '/')
                {
                    // Closing tag
                    tagStartIndex++;
                    RichTextTagParseResult parseResult;
                    bool success = ParseRichTextTag(str, tagStartIndex, tagEndIndex, parseResult);
                    if (success)
                    {
                        auto tagType = parseResult.tagType;
                        if (tagType != RichTextTagType::Unknown)
                        {
                            for (int it = m_FormatStack[(int)tagType].size() - 1; it >= 0; it--)
                            {
                                auto& tag = m_FormatStack[(int)tagType][it];
                                if (tag.endIndex < 0)
                                {
                                    tag.endIndex = actualIndex;
                                    break;
                                }
                            }
                        }

                        cur = tagEndIndex + 1;
                    }
                    else
                    {
                        m_Unicodes.push_back(c);
                        cur++;
                        actualIndex++;
                    }
                }
                else
                {
                    // Opening tag
                    RichTextTagParseResult parseResult;
                    bool success = ParseRichTextTag(str, tagStartIndex, tagEndIndex, parseResult);
                    if (success)
                    {
                        auto tagType = parseResult.tagType;
                        if (tagType != RichTextTagType::Unknown)
                        {
                            auto& tag = m_FormatStack[(int)tagType].emplace_back(RichTextTag{});
                            tag.startIndex = actualIndex;
                            tag.isSelfClosing = parseResult.tag->isSelfClosing;
                            tag.attributes = std::move(parseResult.tag->attributes); // Move the attributes

                            // Parse attributes using the new system
                            if (!tag.attributes.empty())
                            {
                                ParseAttributes(tag, tagType);
                            }

                            // For self-closing tags, set end index immediately
                            if (tag.isSelfClosing)
                            {
                                tag.endIndex = actualIndex;
                            }
                        }

                        cur = tagEndIndex + 1;
                    }
                    else
                    {
                        m_Unicodes.push_back(c);
                        cur++;
                        actualIndex++;
                    }
                }

                break;
            }
            default:
                m_Unicodes.push_back(c);
                cur++;
                actualIndex++;
                break;
            }
        }
    }
    else
    {
        for (int i = 0; i < length; i++)
        {
            m_Unicodes.push_back(unicodes[i]);
        }
    }
}
#pragma endregion RichText tags

void UniTextGenerator::SetActive(bool active)
{
    if (m_IsActive != active)
    {
        m_IsActive = active;
        if (m_IsActive) SetDirty();
        else { MarkTextsAsUnused(); }
    }
}

void UniTextGenerator::SetText(const char* text)
{
    if (!m_Text.empty() && strcmp(m_Text.c_str(), text) == 0) return;

    m_Text.assign(text);
    SetDirty();
}

void UniTextGenerator::AppendText(const char* text)
{
    m_Text.append(text);
    SetDirty();
}

void UniTextGenerator::SetFont(const char* fontName)
{
    if (m_Font != nullptr && strcmp(m_Font->GetName(), fontName) == 0)
        return;

    //m_Font = std::make_shared(UniFontCache::GetInstance()->GetFont(fontName));
    m_Font = UniTextGlobal::GetFontCache()->GetFont(fontName);
    if (m_Font != nullptr) SetDirty();
}

void UniTextGenerator::SetFont(UniFont* uniFont)
{
    if (m_Font != uniFont)
    {
        m_Font = uniFont;
        if (m_Font != nullptr) SetDirty();
    }
}

void UniTextGenerator::SetFontStyle(FontStyle style)
{
    if (m_FontStyle != style)
    {
        m_FontStyle = style;
    }
}

void UniTextGenerator::SetFontSize(int16 fontSize)
{
    if (m_BaseFontSize != fontSize)
    {
        m_BaseFontSize = fontSize;
        SetDirty();
    }
}

void UniTextGenerator::SetAutoSizeRange(int16 minFontSize, int16 maxFontSize)
{
    // Validate input parameters
    if (minFontSize <= 0 || maxFontSize <= 0 || minFontSize > maxFontSize)
    {
        return; // Invalid range, ignore
    }

    if (m_AutoSizeMinFontSize != minFontSize || m_BaseFontSize > maxFontSize)
    {
        m_AutoSizeMinFontSize = static_cast<int16>(minFontSize);
        m_BaseFontSize = std::min(m_BaseFontSize, static_cast<int16>(maxFontSize));
        SetDirty();
    }
}

void UniTextGenerator::SetExtents(float width, float height)
{
    if (m_Extents.x != width || m_Extents.y != height)
    {
        m_Extents.x = width; m_Extents.y = height;
        SetDirty();
    }
}

void UniTextGenerator::SetHorizontalAlignment(TextAlignment alignment)
{
    if (m_HorizontalAlignment != alignment)
    {
        m_HorizontalAlignment = alignment;
        SetDirty();
    }
}

void UniTextGenerator::SetVerticalAlignment(TextAlignment alignment)
{
    if (m_VerticalAlignment != alignment)
    {
        m_VerticalAlignment = alignment;
        SetDirty();
    }
}

void UniTextGenerator::SetPivot(Vector2f pivot)
{
    m_Pivot = pivot;
}

void UniTextGenerator::SetStyle()
{}

void UniTextGenerator::SetLineSpacing(float lineSpacing)
{
    if (m_BaseLineSpacing != lineSpacing)
    {
        m_BaseLineSpacing = lineSpacing;
        SetDirty();
    }
}

void UniTextGenerator::SetCharacterSpacing(float charSpacing)
{
    if (m_BaseCharSpacing != charSpacing)
    {
        m_BaseCharSpacing = charSpacing;
        SetDirty();
    }
}

void UniTextGenerator::SetStrokeSize(float strokeSize)
{
    if (m_BaseStrokeSize != strokeSize)
    {
        m_BaseStrokeSize = strokeSize;
        SetDirty();
    }
}

void UniTextGenerator::SetDirty() noexcept
{
    if (m_IsDirty) return;

    m_IsDirty = true;

#ifndef UNITY
    s_dirtyTexts.push_back(this);
#endif
}

void UniTextGenerator::SetDirty(DirtyType dirtyType, bool isDirty) noexcept
{
    if (m_DirtyFlags.HasBit(static_cast<uint16>(dirtyType)) == isDirty) return;
    m_DirtyFlags.SetBit(static_cast<uint16>(dirtyType), isDirty);

#ifndef UNITY
    if (isDirty) s_dirtyTexts.push_back(this);
#endif
}

void UniTextGenerator::GetTextOffset(Vector2f& offset) noexcept
{
    offset.x = ROUND(-m_Extents.x * m_Pivot.x);
    offset.y = ROUND(m_Extents.y * m_Pivot.y);
}

const bool UniTextGenerator::ShouldBreakWord(const uint16 unicode) noexcept
{
    // Check for common word-breaking characters
    switch (unicode)
    {
        // Spaces, punctuation, and other breaking characters
        case ' ':
        case '\t':
        case '\n':
        case '\r':
        case ',':
        case '.':
        case ';':
        case ':':
        case '!':
        case '?':
        case '/':
        case '\\':
        case '|':
        case '(':
        case ')':
        case '[':
        case ']':
        case '{':
        case '}':
        case '<':
        case '>':
        case '=':
        case '+':
        case '-':
        case '*':
        case '&':
        case '^':
        case '%':
        case '$':
        case '#':
        case '@':
        case '~':
        case '`':
            return true;
    }

    // Latin alphabets (a-z, A-Z)
    if ((unicode >= 'a' && unicode <= 'z') || (unicode >= 'A' && unicode <= 'Z'))
    {
        return false;
    }

    // Numbers (0-9)
    if (unicode >= '0' && unicode <= '9')
    {
        return false;
    }

    // CJK characters - each character is a word
    // CJK Unified Ideographs: 4E00-9FFF
    if (unicode >= 0x4E00 && unicode <= 0x9FFF)
    {
        return true;
    }

    // Japanese Hiragana: 3040-309F
    if (unicode >= 0x3040 && unicode <= 0x309F)
    {
        return true;
    }

    // Japanese Katakana: 30A0-30FF
    if (unicode >= 0x30A0 && unicode <= 0x30FF)
    {
        return true;
    }

    // Korean Hangul: AC00-D7AF
    if (unicode >= 0xAC00 && unicode <= 0xD7AF)
    {
        return true;
    }

    // Default: don't break
    return false;
}

/// <summary>
/// @TODO:
/// Considering using multi-threaded freetype to Load & Render Glyphs
/// then pack them on main thread.
/// This might consume more space, but should be significantly faster.
///
/// THREE-PHASE TEXT LAYOUT APPROACH:
///
/// Phase 1: Pure Character Placement
///   - Insert characters, spaces, and explicit newlines without any wrapping logic
///   - Focus solely on glyph loading, mesh generation, and basic line metrics
///   - No word boundary tracking during this phase
///
/// Phase 2: Word Boundary Analysis & Wrapping
///   - Analyze the entire text to identify word boundaries using ShouldBreakWord()
///   - Apply wrapping logic based on line widths and overflow settings
///   - Handle word-based wrapping for better text flow
///
/// Phase 3: Final Alignment & Positioning
///   - Apply horizontal and vertical alignment
///   - Finalize vertex positions
///
/// This separation provides:
/// - Cleaner, more maintainable code
/// - Consistent word boundary logic in one place
/// - Better performance through reduced redundant calculations
/// - More predictable and debuggable results
/// </summary>
void UniTextGenerator::Rebuild()
{
    Rebuild(true); // Default behavior with auto-sizing enabled
}

void UniTextGenerator::Rebuild(bool enableAutoSizing)
{
    m_IsDirty = false;
    m_MeshData.Clear();

    if (m_Font == nullptr) return;

    PROFILER_START("Preprocess Text");
    UTF16String utfStr(m_Text.c_str());
    PreProcessTexts(utfStr.data, utfStr.length);
    PROFILER_END();

    if (m_Unicodes.empty()) return;

    // Use unified text processor for all text processing
    PROFILER_START("Unified Text Processing");
    if (!ProcessTextUnified())
    {
        // If unified processing failed, we have a serious issue
        // Clear any partial data and return early to avoid processing empty data
        m_Letters.clear();
        m_Lines.clear();
        m_MeshData.Clear();
        PROFILER_END();
        return;
    }
    PROFILER_END();

    // Phase 2: Analyze word boundaries and apply wrapping
    BuildWordBoundaries();
    ProcessWrapping();
    ProcessOverflow();

    // Phase 2.5: Auto-sizing (only when exceeds vertical bounds and enabled)
    // Actually it's auto shinking Down, never expand back again.
    if (enableAutoSizing && HasAutoSize())
    {
        if (ProcessAutoSizing())
        {
            Rebuild(false); // Rebuild with auto-sizing disabled
            return;
        }
    }

    // Phase 3: Apply alignment
    ApplyAlignment();

    m_LastUsedFont = m_Font;
    m_LastFontSize = m_BaseFontSize;
    m_DirtyFlags.Set(static_cast<uint16>(DirtyType::None));
}

void UniTextGenerator::ApplyAlignment() noexcept
{
    if (this->m_MeshData.vertices.empty() || m_Lines.empty())
    {
        return;
    }

    // Calculate total text height and line spacing
    float totalHeight = 0.0f;
    float lineSpacing = m_BaseLineSpacing;

    // First pass: calculate total height including line spacing
    for (size_t i = 0; i < m_Lines.size(); ++i)
    {
        totalHeight += m_Lines[i].height;

        // Add line spacing for all lines except the last one
        if (i < m_Lines.size() - 1)
        {
            totalHeight += lineSpacing;
        }
    }

    // Calculate vertical alignment offset
    float verticalOffset = 0.0f;
    switch (m_VerticalAlignment)
    {
        case TextAlignment::Left: // Top alignment
            verticalOffset = 0.0f;
            break;
        case TextAlignment::Center: // Middle alignment
            // revert y axis to match Unity coordinate system.
            verticalOffset = -(m_Extents.y - totalHeight) * 0.5f;
            break;
        case TextAlignment::Right: // Bottom alignment
            verticalOffset = -(m_Extents.y - totalHeight);
            break;
    }

    // Apply alignment to each line
    // float currentY = verticalOffset;

    for (auto& line : m_Lines)
    {
        // Calculate horizontal alignment offset for this line
        float horizontalOffset = 0.0f;
        switch (m_HorizontalAlignment)
        {
            case TextAlignment::Left:
                horizontalOffset = 0.0f;
                break;
            case TextAlignment::Center:
                horizontalOffset = (m_Extents.x - line.width) * 0.5f;
                break;
            case TextAlignment::Right:
                horizontalOffset = m_Extents.x - line.width;
                break;
        }

        // Store the offset in the line for future reference
        line.offset = horizontalOffset;

        // Apply the offsets to all vertices in this line
        for (int idx = line.startIndex; idx <= line.endIndex; idx++)
        {
            if (idx < 0 || idx >= m_Letters.size())
                continue;

            int vertexIdx = m_Letters[idx].vertexIdx;
            if (vertexIdx >= 0 && vertexIdx + 3 < m_MeshData.vertices.size())
            {
                // Apply horizontal and vertical offsets to all 4 vertices of the quad
                for (int i = 0; i < 4; i++)
                {
                    m_MeshData.vertices[vertexIdx + i].x += horizontalOffset;
                    m_MeshData.vertices[vertexIdx + i].y += verticalOffset;
                }
            }
        }

        // Move to the next line position, this is already handled in the Wrapping process
        // currentY -= line.height + lineSpacing;
    }
}

void UniTextGenerator::IterateRichTextTags(const int curIndex) noexcept
{
    //for (const auto& tagStack : m_FormatStack)
    //static_for<0, static_cast<int>(RichTextTagType::Max)>()([curIndex](int i)
    //    {
    //        IterateRichTextTag<i>(curIndex);
    //    });
    IterateRichTextTag<RichTextTagType::Color>(curIndex);
    IterateRichTextTag<RichTextTagType::Style>(curIndex);
    IterateRichTextTag<RichTextTagType::Size>(curIndex);
    IterateRichTextTag<RichTextTagType::Stroke>(curIndex);
    IterateRichTextTag<RichTextTagType::Underline>(curIndex);
    IterateRichTextTag<RichTextTagType::Deleteline>(curIndex);
    IterateRichTextTag<RichTextTagType::Quad>(curIndex);
    IterateRichTextTag<RichTextTagType::Material>(curIndex);
    IterateRichTextTag<RichTextTagType::Nobr>(curIndex);
}

template<RichTextTagType tagType>
void UniTextGenerator::IterateRichTextTag(const int curIndex) noexcept
{
    const auto& tagStack = m_FormatStack[static_cast<int>(tagType)];
    const auto stackSize = tagStack.size();
    if (stackSize > 0)
    {
        for (size_t i = 0; i < tagStack.size(); i++)
        {
            const auto& tag = tagStack[i];
            if (curIndex == tag.endIndex) 
            {
                // restore
                RestoreRichTextTag<tagType>(tag);
                break;
            }
        }
        for (size_t i = 0; i < tagStack.size(); i++) 
        {
            const auto& tag = tagStack[i];
            if (curIndex == tag.startIndex) 
            {
                // apply tag Value
                ApplyRichTextTag<tagType>(tag);
                break;
            }
        }
    }
}

template<RichTextTagType tagType>
void UniTextGenerator::ApplyRichTextTag(const RichTextTag& tag) noexcept
{
    if constexpr (tagType == RichTextTagType::Color)
    {
        // Use unified value access
        auto colorValue = tag.GetValue<Color>();
        if (colorValue.has_value())
        {
            m_PropertyModifier.color.push_back(colorValue.value());
        }
    }
    else if constexpr (tagType == RichTextTagType::Style)
    {
        // Use unified value access
        auto styleValue = tag.GetValue<FontStyle>();
        if (styleValue.has_value())
        {
            m_PropertyModifier.style.push_back(styleValue.value());
        }
    }
    else if constexpr (tagType == RichTextTagType::Size)
    {
        // Use unified value access
        auto sizeValue = tag.GetValue<int>();
        if (sizeValue.has_value())
        {
            m_PropertyModifier.fontSize.push_back(sizeValue.value());
        }
    }
    else if constexpr (tagType == RichTextTagType::Quad)
    {
        // Quad tags render a colored rectangle at the current position
        // Get attributes with default values using predefined keys
        int x = tag.GetAttribute<int>(PredefinedKeys::x).value_or(0);
        int y = tag.GetAttribute<int>(PredefinedKeys::y).value_or(0);
        int width = tag.GetAttribute<int>(PredefinedKeys::width).value_or(10);
        int height = tag.GetAttribute<int>(PredefinedKeys::height).value_or(10);

        // Get current color or use default
        auto quadColor = m_PropertyModifier.color.size() > 0 ? m_PropertyModifier.color.back() : m_BaseColor;

        // Calculate position relative to current pen position (baseline)
        Vector2f quadPos = { m_Pen.x + static_cast<float>(x), m_Pen.y + static_cast<float>(y) };
        Vector2f quadSize = { static_cast<float>(width), static_cast<float>(height) };

        // Add the quad to mesh data
        AddEmptyQuad(quadPos, quadSize, quadColor);
    }
    else if constexpr (tagType == RichTextTagType::Nobr)
    {
        // No break tag - prevents word wrapping
        m_PropertyModifier.isInsideNobr = true;
    }
    else if constexpr (tagType == RichTextTagType::Underline)
    {
        m_PropertyModifier.isUnderline = true;
    }
    else if constexpr (tagType == RichTextTagType::Deleteline)
    {
        m_PropertyModifier.isDeleteline = true;
    }
}

template<RichTextTagType tagType>
void UniTextGenerator::RestoreRichTextTag(const RichTextTag& tag) noexcept
{
    if constexpr (tagType == RichTextTagType::Color)
    {
        if (m_PropertyModifier.color.size() > 0) m_PropertyModifier.color.pop_back();
    }
    else if constexpr (tagType == RichTextTagType::Style)
    {
        if (m_PropertyModifier.style.size() > 0) m_PropertyModifier.style.pop_back();
    }
    else if constexpr (tagType == RichTextTagType::Size)
    {
        if (m_PropertyModifier.fontSize.size() > 0) m_PropertyModifier.fontSize.pop_back();
    }
    else if constexpr (tagType == RichTextTagType::Quad)
    {
        // Quad tags don't need restoration as they don't modify text properties
        // Self-closing quad tags are handled automatically
    }
    else if constexpr (tagType == RichTextTagType::Nobr)
    {
        // End of no break tag - re-enable word wrapping
        m_PropertyModifier.isInsideNobr = false;
    }
    else if constexpr (tagType == RichTextTagType::Underline)
    {
        m_PropertyModifier.isUnderline = false;
    }
    else if constexpr (tagType == RichTextTagType::Deleteline)
    {
        m_PropertyModifier.isDeleteline = false;
    }
}

void UniTextGenerator::AddGlyphQuad(const Vector2f& offset, float scale, const Color& color, const GlyphInfo& glyph, const SizedPage* page) noexcept
{
    if (!m_Font)
        return;

    const int padding = m_Font->GetPadding();

    // float w_scale = (scale - 1.0f) * glyph.bounds.width;
    const float h_scale = (scale - 1.0f) * glyph.bounds.height;
    // spread scale will cause uv scale with wrong value
    const float spread_scale = 1.0f;//static_cast<float>(glyph.spreadSize) * (scale - 1.0f);

    // only scale on top|right, don't change bottom|left
    const float left = glyph.bounds.x - padding + spread_scale;
    const float right = glyph.bounds.x + glyph.bounds.width * scale + padding - spread_scale;
    const float top = glyph.bounds.y  + h_scale + padding - spread_scale;
    const float bottom = glyph.bounds.y - glyph.bounds.height - padding + spread_scale;

    const float invWidth = 1.0f / page->GetTexture()->GetWidth();
    const float invHeight = 1.0f / page->GetTexture()->GetHeight();
    const float x_in_atlas = glyph.rect.x + page->textureEntry.textureRect.x;
    const float y_in_atlas = glyph.rect.y + page->textureEntry.textureRect.y;
    const float textureIndex = static_cast<float>(page->textureEntry.textureIndex);

    // this is the unity style quad
    // 0 -- 1
    // |    |
    // 3 -- 2
    const Vector2f vert_tl { offset.x + left, offset.y + top };
    const Vector2f vert_tr { offset.x + right, vert_tl.y };
    const Vector2f vert_br { vert_tr.x, offset.y + bottom };
    const Vector2f vert_bl { vert_tl.x, vert_br.y };
    m_MeshData.vertices.emplace_back(vert_tl);
    m_MeshData.vertices.emplace_back(vert_tr);
    m_MeshData.vertices.emplace_back(vert_br);
    m_MeshData.vertices.emplace_back(vert_bl);

    m_MeshData.colors.emplace_back(color);
    m_MeshData.colors.emplace_back(color);
    m_MeshData.colors.emplace_back(color);
    m_MeshData.colors.emplace_back(color);

    const Vector4f uv_tl{ (x_in_atlas - padding + spread_scale) * invWidth, (y_in_atlas - padding - spread_scale) * invHeight, textureIndex };
    const Vector4f uv_tr{ (x_in_atlas + glyph.rect.width + padding - spread_scale) * invWidth, uv_tl.y, textureIndex };
    const Vector4f uv_br{ uv_tr.x, (y_in_atlas + glyph.rect.height + padding + spread_scale) * invHeight, textureIndex };
    const Vector4f uv_bl{ uv_tl.x, uv_br.y, textureIndex };
    m_MeshData.uvs.emplace_back(uv_tl);
    m_MeshData.uvs.emplace_back(uv_tr);
    m_MeshData.uvs.emplace_back(uv_br);
    m_MeshData.uvs.emplace_back(uv_bl);
}

void UniTextGenerator::AddEmptyQuad(const Vector2f& bottomLeft, const Vector2f& size, const Color& color) noexcept
{
    const Vector2f vert_tl { bottomLeft.x, bottomLeft.y + size.y };
    const Vector2f vert_tr { bottomLeft.x + size.x, vert_tl.y };
    const Vector2f vert_br { vert_tr.x, bottomLeft.y };
    const Vector2f vert_bl { bottomLeft.x, vert_br.y };
    m_MeshData.vertices.emplace_back(vert_tl);
    m_MeshData.vertices.emplace_back(vert_tr);
    m_MeshData.vertices.emplace_back(vert_br);
    m_MeshData.vertices.emplace_back(vert_bl);

    m_MeshData.colors.emplace_back(color);
    m_MeshData.colors.emplace_back(color);
    m_MeshData.colors.emplace_back(color);
    m_MeshData.colors.emplace_back(color);

    m_MeshData.uvs.emplace_back(Vector4f{0.0f, 0.0f, 0.0f, 0.0f});
    m_MeshData.uvs.emplace_back(Vector4f{0.0f, 0.0f, 0.0f, 0.0f});
    m_MeshData.uvs.emplace_back(Vector4f{0.0f, 0.0f, 0.0f, 0.0f});
    m_MeshData.uvs.emplace_back(Vector4f{0.0f, 0.0f, 0.0f, 0.0f});
}

void UniTextGenerator::AddLineQuad(const Vector2f& startPos, const Vector2f& endPos, float thickness, const Color& color) noexcept
{
    // Calculate line direction and perpendicular vector for thickness
    Vector2f direction = endPos - startPos;
    float length = sqrtf(direction.x * direction.x + direction.y * direction.y);

    if (length < 0.001f) return; // Skip zero-length lines

    // Normalize direction
    direction.x /= length;
    direction.y /= length;

    // Calculate perpendicular vector (rotated 90 degrees)
    Vector2f perpendicular = { -direction.y, direction.x };
    Vector2f halfThickness = perpendicular * (thickness * 0.5f);

    // Calculate quad vertices
    const Vector2f vert_tl = startPos + halfThickness;
    const Vector2f vert_tr = endPos + halfThickness;
    const Vector2f vert_br = endPos - halfThickness;
    const Vector2f vert_bl = startPos - halfThickness;

    // Add vertices in correct order for quad rendering
    m_MeshData.vertices.emplace_back(vert_tl);
    m_MeshData.vertices.emplace_back(vert_tr);
    m_MeshData.vertices.emplace_back(vert_br);
    m_MeshData.vertices.emplace_back(vert_bl);

    // Add colors
    m_MeshData.colors.emplace_back(color);
    m_MeshData.colors.emplace_back(color);
    m_MeshData.colors.emplace_back(color);
    m_MeshData.colors.emplace_back(color);

    // Get UV coordinates from a reference glyph (like '*') to ensure proper texture sampling
    const auto referenceUV = GetGlyphUV('*');
    m_MeshData.uvs.emplace_back(referenceUV);
    m_MeshData.uvs.emplace_back(referenceUV);
    m_MeshData.uvs.emplace_back(referenceUV);
    m_MeshData.uvs.emplace_back(referenceUV);
}

Vector4f UniTextGenerator::GetGlyphUV(const uint16 character) noexcept
{
    if (!m_Font)
        return Vector4f(0.0f, 0.0f, 0.0f, 0.0f);

    // Get UV coordinates from the specified character
    // This ensures we sample from a valid part of the texture atlas
    int fontSize = m_PropertyModifier.fontSize.size() > 0 ? m_PropertyModifier.fontSize.back() : m_BaseFontSize;
    int roundedSize = RoundFontSize(fontSize, m_Font->GetMinFontSize(), m_Font->GetMaxFontSize());

    GlyphInfo* referenceGlyph = nullptr;
    GlyphLoadParam param = { character, roundedSize, RenderMode::NotSpecified };

    if (m_Font->LoadGlyph<false>(param, &referenceGlyph) && referenceGlyph)
    {
        auto page = m_Font->GetGlyphPage(roundedSize, *referenceGlyph);
        if (page != nullptr)
        {
            // Calculate UV coordinates similar to AddGlyphQuad
            const int padding = m_Font->GetPadding();
            const float spread_scale = 1.0f;
            const float invWidth = 1.0f / page->GetTexture()->GetWidth();
            const float invHeight = 1.0f / page->GetTexture()->GetHeight();
            const float x_in_atlas = referenceGlyph->rect.x + page->textureEntry.textureRect.x;
            const float y_in_atlas = referenceGlyph->rect.y + page->textureEntry.textureRect.y;
            const float textureIndex = static_cast<float>(page->textureEntry.textureIndex);

            // Return a single UV coordinate from the center of the reference glyph
            float centerU = (x_in_atlas + referenceGlyph->rect.width * 0.5f) * invWidth;
            float centerV = (y_in_atlas + referenceGlyph->rect.height * 0.5f) * invHeight;

            return Vector4f(centerU, centerV, textureIndex, 0.0f);
        }
    }

    // Fallback: return (0,0,0,0) if we can't get a reference glyph
    return Vector4f(0.0f, 0.0f, 0.0f, 0.0f);
}

void UniTextGenerator::OffsetQuad(int startIndex, int endIndex, const Vector2f& offset) noexcept
{
    // Apply offset to all vertices in the specified range of letters
    for (int letterIdx = startIndex; letterIdx <= endIndex; ++letterIdx) {
        if (letterIdx >= 0 && letterIdx < static_cast<int>(m_Letters.size())) {
            const auto& letter = m_Letters[letterIdx];

            // Skip spaces (they have vertexIdx = -1)
            if (letter.vertexIdx >= 0 && letter.vertexIdx + 3 < static_cast<int>(m_MeshData.vertices.size())) {
                // Apply offset to all 4 vertices of the quad
                for (int i = 0; i < 4; ++i) {
                    m_MeshData.vertices[letter.vertexIdx + i].x += offset.x;
                    m_MeshData.vertices[letter.vertexIdx + i].y += offset.y;
                }
            }
        }
    }
}

void UniTextGenerator::ProcessWrapping() noexcept
{
    if (m_Lines.empty() || m_HorizontalOverflow == TextOverflow::Overflow)
    {
        return; // No wrapping needed
    }

    // Process each line for wrapping
    // Note: We iterate by index because WrapLine() may insert new lines
    for (int lineIndex = 0; lineIndex < static_cast<int>(m_Lines.size()); ++lineIndex)
    {
        auto& line = m_Lines[lineIndex];

        // Check if line exceeds horizontal bounds
        if (line.width > m_Extents.x)
        {
            if (m_HorizontalOverflow == TextOverflow::Wrap)
            {
                WrapLine(lineIndex);
                // After wrapping, the current line may have been split
                // The loop will continue and check the newly created line(s) as well
            }
            else
            {
                ApplyOverflow(lineIndex);
            }
        }
    }
}

void UniTextGenerator::WrapLine(int lineIndex) noexcept
{
    if (lineIndex >= static_cast<int>(m_Lines.size())) return;

    // 这里有个bug, 采用auto&的话, 如果下面insert导致memory重新分配, 那么line会指向错误的内存.
    auto& line = m_Lines[lineIndex];

    // Find the best wrap point using word boundaries
    int wrapPoint = -1;
    float currentWidth = 0.0f;

    // Scan through letters in this line to find wrap point
    for (int letterIdx = line.startIndex; letterIdx <= line.endIndex; ++letterIdx)
    {
        if (letterIdx >= static_cast<int>(m_Letters.size())) break;

        const auto& letter = m_Letters[letterIdx];
        currentWidth += letter.advance.x + m_BaseCharSpacing;

        // If we exceed bounds, find the best word boundary to wrap at
        if (currentWidth > m_Extents.x) 
        {
            wrapPoint = FindWordBoundaryBefore(letterIdx);

            // Ensure wrap point is within the current line
            if (wrapPoint < line.startIndex) 
            {
                wrapPoint = letterIdx - 1; // Fall back to character wrap
            }

            break;
        }
    }

    // If we found a valid wrap point, split the line
    if (wrapPoint >= line.startIndex && wrapPoint < line.endIndex) 
    {
        // Create new line for wrapped content
        Line newLine;
        newLine.startIndex = wrapPoint + 1;
        newLine.endIndex = line.endIndex;

        // Calculate widths for both lines
        float firstLineWidth = 0.0f;
        float secondLineWidth = 0.0f;

        for (int i = line.startIndex; i <= wrapPoint; ++i) 
        {
            if (i < static_cast<int>(m_Letters.size())) 
            {
                firstLineWidth += m_Letters[i].advance.x + m_BaseCharSpacing;
            }
        }

        for (int i = newLine.startIndex; i <= newLine.endIndex; ++i) 
        {
            if (i < static_cast<int>(m_Letters.size())) 
            {
                secondLineWidth += m_Letters[i].advance.x + m_BaseCharSpacing;
            }
        }

        // Update original line
        line.endIndex = wrapPoint;
        line.width = firstLineWidth;

        // Set new line properties
        newLine.width = secondLineWidth;
        newLine.height = line.height; // Inherit height
        newLine.maxFontSize = line.maxFontSize;

        // Calculate vertical offset for the new line
        float lineHeight = line.height + m_BaseLineSpacing;
        // Reset horizontal position for wrapped content
        float horizontalReset = -firstLineWidth;  // Move back to line start
        Vector2f offset(horizontalReset, -lineHeight);
        // Offset all vertices in the wrapped portion to the new line position
        OffsetQuad(newLine.startIndex, newLine.endIndex, offset);

        auto oldLineWidth = line.width;

        // Insert new line after current line here to avoid memory corruption
        m_Lines.insert(m_Lines.begin() + lineIndex + 1, newLine);

        // IMPORTANT: Move all subsequent lines down to make room for the new line
        // We need to offset all lines that come after the newly inserted line
        Vector2f subsequentLinesOffset(0.0f, -lineHeight);
        for (int subsequentLineIdx = lineIndex + 2; subsequentLineIdx < static_cast<int>(m_Lines.size()); ++subsequentLineIdx)
        {
            const auto& subsequentLine = m_Lines[subsequentLineIdx];
            OffsetQuad(subsequentLine.startIndex, subsequentLine.endIndex, subsequentLinesOffset);
        }

        // Check if the new line also needs wrapping
        if (newLine.width > m_Extents.x &&
            newLine.endIndex > newLine.startIndex &&
            secondLineWidth < oldLineWidth) 
        {  
            // Only wrap if we made progress
            WrapLine(lineIndex + 1);
        }
    }
}

void UniTextGenerator::ApplyOverflow(int lineIndex) noexcept
{
    if (lineIndex >= static_cast<int>(m_Lines.size())) return;

    auto& line = m_Lines[lineIndex];

    switch (m_HorizontalOverflow) {
        case TextOverflow::Truncate:
            // Find where to truncate
            {
                float currentWidth = 0.0f;
                int truncatePoint = line.endIndex;

                for (int letterIdx = line.startIndex; letterIdx <= line.endIndex; ++letterIdx) {
                    if (letterIdx >= static_cast<int>(m_Letters.size())) break;

                    const auto& letter = m_Letters[letterIdx];
                    currentWidth += letter.advance.x + m_BaseCharSpacing;

                    if (currentWidth > m_Extents.x) {
                        truncatePoint = letterIdx - 1;
                        break;
                    }
                }

                // Update line to truncate at this point
                line.endIndex = truncatePoint;

                // Recalculate line width
                float newWidth = 0.0f;
                for (int i = line.startIndex; i <= line.endIndex; ++i) {
                    if (i < static_cast<int>(m_Letters.size())) {
                        newWidth += m_Letters[i].advance.x + m_BaseCharSpacing;
                    }
                }
                line.width = newWidth;
            }
            break;

        case TextOverflow::Ellipse:
            // Similar to truncate, but reserve space for ellipsis
            // TODO: Implement ellipsis insertion logic
            break;

        default:
            break;
    }
}

void UniTextGenerator::BuildWordBoundaries() noexcept
{
    m_Words.clear();

    if (m_Unicodes.empty()) return;

    int wordStart = -1;

    for (int i = 0; i < static_cast<int>(m_Unicodes.size()); ++i)
    {
        uint16 unicode = m_Unicodes[i];
        bool isWordBreaker = ShouldBreakWord(unicode);

        if (!isWordBreaker)
        {
            // Start a new word if we don't have one
            if (wordStart == -1)
            {
                wordStart = i;
            }
        } else
        {
            // End current word if we have one
            if (wordStart != -1)
            {
                Word word;
                word.startIndex = wordStart;
                word.endIndex = i - 1;
                m_Words.emplace_back(word);
                wordStart = -1;
            }
        }
    }

    // Handle word at end of text
    if (wordStart != -1)
    {
        Word word;
        word.startIndex = wordStart;
        word.endIndex = static_cast<int>(m_Unicodes.size()) - 1;
        m_Words.emplace_back(word);
    }
}

int UniTextGenerator::FindWordBoundaryBefore(int letterIndex) noexcept
{
    // Find the word that contains or comes before this letter index
    for (auto it = m_Words.rbegin(); it != m_Words.rend(); ++it) 
    {
        if (it->endIndex < letterIndex) 
        {
            // This word ends before our letter, so wrap after this word
            return it->endIndex;
        }
        if (it->startIndex <= letterIndex && letterIndex <= it->endIndex) 
        {
            // Letter is inside this word, wrap before the word starts
            if (it->startIndex > 0) 
            {
                return it->startIndex - 1;
            }
        }
    }

    // No good word boundary found, return the letter before current position
    return letterIndex > 0 ? letterIndex - 1 : 0;
}

void UniTextGenerator::ProcessOverflow() noexcept
{
    if (m_Lines.empty() || m_VerticalOverflow == TextOverflow::Overflow) 
    {
        return; // No vertical overflow handling needed
    }

    // Calculate total text height
    float totalHeight = 0.0f;
    for (size_t i = 0; i < m_Lines.size(); ++i) 
    {
        totalHeight += m_Lines[i].height;
        if (i < m_Lines.size() - 1) 
        {
            totalHeight += m_BaseLineSpacing;
        }
    }

    // Check if text exceeds vertical bounds
    if (totalHeight > m_Extents.y) 
    {
        switch (m_VerticalOverflow) 
        {
            case TextOverflow::Truncate:
                {
                    // Remove lines that exceed vertical bounds
                    float currentHeight = 0.0f;
                    size_t lastValidLine = 0;

                    for (size_t i = 0; i < m_Lines.size(); ++i) 
                    {
                        float lineHeight = m_Lines[i].height;
                        if (i > 0) lineHeight += m_BaseLineSpacing;

                        if (currentHeight + lineHeight > m_Extents.y) 
                        {
                            break;
                        }

                        currentHeight += lineHeight;
                        lastValidLine = i;
                    }

                    // Remove lines beyond the last valid line
                    if (lastValidLine + 1 < m_Lines.size()) 
                    {
                        m_Lines.resize(lastValidLine + 1);
                    }
                }
                break;

            case TextOverflow::Ellipse:
                // TODO: Implement vertical ellipsis logic
                // For now, just truncate like above
                {
                    float currentHeight = 0.0f;
                    size_t lastValidLine = 0;

                    for (size_t i = 0; i < m_Lines.size(); ++i) 
                    {
                        float lineHeight = m_Lines[i].height;
                        if (i > 0) lineHeight += m_BaseLineSpacing;

                        if (currentHeight + lineHeight > m_Extents.y) 
                        {
                            break;
                        }

                        currentHeight += lineHeight;
                        lastValidLine = i;
                    }

                    if (lastValidLine + 1 < m_Lines.size()) 
                    {
                        m_Lines.resize(lastValidLine + 1);
                    }
                }
                break;

            default:
                break;
        }
    }
}

bool UniTextGenerator::ProcessAutoSizing() noexcept
{
    if (m_Lines.empty())
    {
        return false; // No auto-sizing needed or invalid range
    }

    // Calculate total text height
    float totalHeight = 0.0f;
    for (size_t i = 0; i < m_Lines.size(); ++i)
    {
        totalHeight += m_Lines[i].height;
        if (i < m_Lines.size() - 1)
        {
            totalHeight += m_BaseLineSpacing;
        }
    }

    // Only auto-size when text exceeds vertical bounds
    if (totalHeight <= m_Extents.y)
    {
        return false; // Text fits, no auto-sizing needed
    }

    // Binary search for the optimal font size
    int16 minSize = m_AutoSizeMinFontSize;
    int16 maxSize = m_BaseFontSize; // Don't go larger than current size
    int16 bestSize = minSize;

    // If current font size is already at or below minimum, no point in auto-sizing
    if (m_BaseFontSize <= minSize)
    {
        return false;
    }

    while (minSize <= maxSize)
    {
        int16 testSize = (minSize + maxSize) / 2;

        // Test if this font size fits
        if (CheckFontSizeFits(testSize))
        {
            bestSize = testSize;
            minSize = testSize + 1; // Try larger size
        }
        else
        {
            maxSize = testSize - 1; // Try smaller size
        }
    }

    // Apply the best font size found
    if (bestSize != m_BaseFontSize && bestSize >= m_AutoSizeMinFontSize)
    {
        m_BaseFontSize = bestSize;
        return true;
    }

    return false;
}

bool UniTextGenerator::CheckFontSizeFits(int16 testFontSize) noexcept
{
    if (!m_Font) return false;

    float ascender = ROUND(m_Font->GetAscender(testFontSize));
    float descender = ROUND(m_Font->GetDescender(testFontSize));
    float lineSpacing = ascender - descender;

    // Estimate total height based on number of lines and line spacing
    // This is a simplified calculation for performance
    float estimatedHeight = 0.0f;
    if (!m_Lines.empty())
    {
        // Use the ratio of new font size to old font size to estimate new height
        // Note that RichText sizes are not considered.
        float sizeRatio = static_cast<float>(testFontSize) / static_cast<float>(m_BaseFontSize);

        for (size_t i = 0; i < m_Lines.size(); ++i)
        {
            estimatedHeight += m_Lines[i].height * sizeRatio;
            if (i < m_Lines.size() - 1)
            {
                estimatedHeight += m_BaseLineSpacing;
            }
        }
    }

    return estimatedHeight <= m_Extents.y;
}

void UniTextGenerator::MarkTextsAsUnused()
{
    if (m_Unicodes.size() > 0)
    {
        auto usingFont = m_LastUsedFont != nullptr ? m_LastUsedFont : m_Font;
        if (usingFont != nullptr)
        {
            m_PropertyModifier.clear();
            const bool hasRichText = HasRichText();
            for (int i = 0; i < m_Unicodes.size(); i++)
            {
                if (hasRichText) 
                {  
                    // IterateRichTextTags(i);
                    // only need font size for properly handle sized based release.
                    IterateRichTextTag<RichTextTagType::Size>(i);
                }

                int fontSize = m_PropertyModifier.fontSize.size() > 0 ? m_PropertyModifier.fontSize.back() : m_LastFontSize;
                usingFont->UnloadGlyph(m_Unicodes[i], fontSize);
            }
        }
    }
}

bool UniTextGenerator::ProcessTextUnified() noexcept
{
    if (!m_UnifiedProcessor || !m_Font) {
        return false;
    }

    // Prepare processing parameters
    TextProcessingParams params;
    params.generator = this;

    // Determine direction based on RTL setting
    params.direction = HasRTL() ? ProcessingDirection::RightToLeft : ProcessingDirection::LeftToRight;

    // Process the text using the unified processor
    return m_UnifiedProcessor->ProcessText(params);
}

//////////////////////////////////////////////////////////////////////////
// Public APIs for Text Processors
//////////////////////////////////////////////////////////////////////////

void UniTextGenerator::ResetPenHorizontally() noexcept
{
    if (HasRTL())
    {
        // For RTL, start from the right edge of the text area
        m_Pen.x = m_Pen.offset.x + m_Extents.x;
    }
    else
    {
        // For LTR, start from the left edge
        m_Pen.x = m_Pen.offset.x;
    }
}

void UniTextGenerator::InitializePenWithOffset() noexcept
{
    m_Pen.Reset();
    GetTextOffset(m_Pen.offset);

    const float characterSize = static_cast<float>(m_BaseFontSize);
    const float ascender = ROUND(m_Font->GetAscender(m_BaseFontSize));
    const float descender = ROUND(m_Font->GetDescender(m_BaseFontSize));
    ResetPenHorizontally();
    m_Pen.y = m_Pen.offset.y - ascender;
    m_Pen.lineSpacing = ascender - descender;
    m_Pen.minX = m_Pen.minY = characterSize;
}

float UniTextGenerator::GetKerning(uint16 first, uint16 second) const noexcept
{
    return m_Font ? m_Font->GetKerning(first, second) : 0.0f;
}

bool UniTextGenerator::InsertCharacter(uint16 unicode) noexcept
{
    int fontSize = m_PropertyModifier.fontSize.size() > 0 ? m_PropertyModifier.fontSize.back() : m_BaseFontSize;
    GlyphInfo* glyph;
    m_Font->SetFontSizeForRendering(fontSize);

    int roundedSize = RoundFontSize(fontSize, m_Font->GetMinFontSize(), m_Font->GetMaxFontSize());
    GlyphLoadParam param = { unicode, roundedSize, RenderMode::NotSpecified };
    if (m_Font->LoadGlyph(param, &glyph))
    {
        float scale = static_cast<float>(fontSize) / roundedSize;

        // Generate mesh for the glyph
        auto page = m_Font->GetGlyphPage(roundedSize, *glyph);
        if (page != nullptr)
        {
            auto textColor = m_PropertyModifier.color.size() > 0 ? m_PropertyModifier.color.back() : m_BaseColor;
            AddGlyphQuad({ m_Pen.x, m_Pen.y }, scale, textColor, *glyph, page);
        }

        // Simple layout without wrapping - just add to current line
        auto& curLine = m_Lines.back();
        float glyphWidth = glyph->advance.x * scale;
        float glyphHeight = glyph->bounds.height * scale;

        // Create letter entry
        auto& letter = m_Letters.emplace_back(Letter{
            Vector2f(ROUND(glyphWidth), ROUND(glyph->advance.y * scale)),
            static_cast<int>(m_MeshData.vertices.size() - 4) // Point to the quad we just added
        });

        // Add underline and deleteline if needed
        auto textColor = m_PropertyModifier.color.size() > 0 ? m_PropertyModifier.color.back() : m_BaseColor;
        float lineThickness = fontSize * 0.05f; // 5% of font size for line thickness

        if (m_PropertyModifier.isUnderline)
        {
            // Position underline below baseline
            float underlineY = m_Pen.y - fontSize * 0.1f; // 10% below baseline
            Vector2f startPos = { m_Pen.x, underlineY };
            Vector2f endPos = { m_Pen.x + glyphWidth, underlineY };
            AddLineQuad(startPos, endPos, lineThickness, textColor);
        }

        if (m_PropertyModifier.isDeleteline)
        {
            // Position deleteline at middle height of the character
            float deletelineY = m_Pen.y + fontSize * 0.3f; // 30% above baseline (roughly middle)
            Vector2f startPos = { m_Pen.x, deletelineY };
            Vector2f endPos = { m_Pen.x + glyphWidth, deletelineY };
            AddLineQuad(startPos, endPos, lineThickness, textColor);
        }

        // Update pen and line metrics
        m_Pen.x += glyphWidth + m_BaseCharSpacing;
        curLine.endIndex = m_Pen.currentCharIdx;
        curLine.width += glyphWidth + m_BaseCharSpacing;
        curLine.height = std::max(curLine.height, glyphHeight);
        curLine.maxFontSize = std::max(fontSize, curLine.maxFontSize);

        // Update bounding box
        float glyphBottom = glyph->bounds.y - glyphHeight;
        float glyphTop = glyph->bounds.y;
        m_Pen.minY = std::min(glyphBottom, m_Pen.minY);
        m_Pen.maxY = std::max(glyphTop, m_Pen.maxY);
        m_Pen.lineSpacing = std::max(m_Pen.lineSpacing, m_Pen.maxY - m_Pen.minY);
    }

    return true;
}

bool UniTextGenerator::InsertSpace(uint16 unicode, int count) noexcept
{
    GlyphInfo* glyph;
    int fontSize = m_PropertyModifier.fontSize.size() > 0 ? m_PropertyModifier.fontSize.back() : m_BaseFontSize;
    auto& curLine = m_Lines.back();
    curLine.maxFontSize = std::max(fontSize, curLine.maxFontSize);

    int roundedSize = RoundFontSize(fontSize, m_Font->GetMinFontSize(), m_Font->GetMaxFontSize());
    GlyphLoadParam param = { unicode, roundedSize, RenderMode::NotSpecified };
    if (m_Font->LoadGlyph<false>(param, &glyph))
    {
        // Calculate scale for the space character
        float scale = static_cast<float>(fontSize) / roundedSize;

        // Add the space to the current line (no wrapping logic here)
        auto& letter = m_Letters.emplace_back(Letter{
            Vector2f(ROUND(glyph->advance.x * scale * count), ROUND(glyph->advance.y * scale)),
            -1 // No visible quad for spaces
        });

        // Add underline and deleteline for spaces if needed
        auto textColor = m_PropertyModifier.color.size() > 0 ? m_PropertyModifier.color.back() : m_BaseColor;
        float lineThickness = fontSize * 0.05f; // 5% of font size for line thickness
        float spaceWidth = letter.advance.x;

        if (m_PropertyModifier.isUnderline)
        {
            // Position underline below baseline
            float underlineY = m_Pen.y - fontSize * 0.1f; // 10% below baseline
            Vector2f startPos = { m_Pen.x, underlineY };
            Vector2f endPos = { m_Pen.x + spaceWidth, underlineY };
            AddLineQuad(startPos, endPos, lineThickness, textColor);
        }

        if (m_PropertyModifier.isDeleteline)
        {
            // Position deleteline at middle height of the character
            float deletelineY = m_Pen.y + fontSize * 0.3f; // 30% above baseline (roughly middle)
            Vector2f startPos = { m_Pen.x, deletelineY };
            Vector2f endPos = { m_Pen.x + spaceWidth, deletelineY };
            AddLineQuad(startPos, endPos, lineThickness, textColor);
        }

        // Update pen position
        m_Pen.x += letter.advance.x + m_BaseCharSpacing;

        // Update line information
        curLine.endIndex = m_Pen.currentCharIdx;
        curLine.width += letter.advance.x + m_BaseCharSpacing;

        return true;
    }

    return false;
}

bool UniTextGenerator::InsertNewLine() noexcept
{
    // Finalize the current line
    auto& lastLine = m_Lines.back();
    lastLine.endIndex = m_Pen.currentCharIdx - 1;

    // Create a new line
    auto& newLine = m_Lines.emplace_back(Line{});
    newLine.startIndex = m_Pen.currentCharIdx; // Start after the newline character

    // Reset pen position for new line
    ResetPenHorizontally();
    m_Pen.y -= (m_Pen.lineSpacing + m_BaseLineSpacing);

    return true;
}