Using pre-set license
Built from '2022.3/release' branch; Version is '2022.3.61f1 (6c53ebaf375d) revision 7099371'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 65205 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.61f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
E:/trunk_base/Young/UniTextGit/UniTextUnityProj
-logFile
Logs/AssetImportWorker1.log
-srvPort
11213
Successfully changed project path to: E:/trunk_base/Young/UniTextGit/UniTextUnityProj
E:/trunk_base/Young/UniTextGit/UniTextUnityProj
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [36224]  Target information:

Player connection [36224]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 767201807 [EditorId] 767201807 [Version] 1048832 [Id] WindowsEditor(7,YOUNGXIANG-PC1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [36224]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 767201807 [EditorId] 767201807 [Version] 1048832 [Id] WindowsEditor(7,YOUNGXIANG-PC1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [36224] Host joined multi-casting on [***********:54997]...
Player connection [36224] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
Refreshing native plugins compatible for Editor in 0.99 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61f1 (6c53ebaf375d)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/trunk_base/Young/UniTextGit/UniTextUnityProj/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 (ID=0x2882)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56892
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005559 seconds.
- Loaded All Assemblies, in  0.239 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
