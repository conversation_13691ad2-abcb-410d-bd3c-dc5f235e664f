﻿  UniGlyphData.cpp
  UniTexture.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/Common/UniTexture.cpp”)
  
  UniCustomFont.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/Font/Custom/UniCustomFont.cpp”)
  
  UniFontFreeType.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/Font/FreeType/UniFontFreeType.cpp”)
  
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\FreeType\UniFontFreeType.cpp(501,13): warning C4838: 从“int”转换到“UniText::uint8”需要收缩转换
  MaxRectsGlyphPacker.cpp
  UniGlyphPacker.cpp
  UniFT_Renderer.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/Font/GlyphRender/UniFT_Renderer.cpp”)
  
  UniSDF_Renderer.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniSDF_Renderer.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/Font/GlyphRender/UniSDF_Renderer.cpp”)
  
  SDFGenerator.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\SDFGenerator.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  UniPostProcessor.cpp
  AndroidFontFallback.cpp
  AppleFontFallback.cpp
  DefaultFontFallback.cpp
  WindowsFontFallback.cpp
  UniFont.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/Font/UniFont.cpp”)
  
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFont.cpp(988,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  UniFontAtlasEntry.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/Font/UniFontAtlasEntry.cpp”)
  
  UniFontFallback.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/Font/UniFontFallback.cpp”)
  
  UniTextAPIExport.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/UniTextAPIExport.cpp”)
  
  UniTextGenerator.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/UniTextGenerator.cpp”)
  
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1428,21): error C2672: “UniText::UniTextGenerator::RichTextTag::GetAttribute”: 未找到匹配的重载函数
      E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.h(274,26):
      可能是“std::optional<_Ty> UniText::UniTextGenerator::RichTextTag::GetAttribute(UniText::UniStringView) const”
          E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1428,55):
          “初始化”: 无法从“const UniText::UniString”转换为“UniText::UniStringView”
              E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1428,55):
              没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
      E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1428,21):
      模板实例化上下文(最早的实例化上下文)为
          E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1359,5):
          查看对正在编译的函数 模板 实例化“void UniText::UniTextGenerator::IterateRichTextTag<UniText::RichTextTagType::Quad>(const int) noexcept”的引用
          E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1387,17):
          查看对正在编译的函数 模板 实例化“void UniText::UniTextGenerator::ApplyRichTextTag<UniText::RichTextTagType::Quad>(const UniText::UniTextGenerator::RichTextTag &) noexcept”的引用
  
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1429,21): error C2672: “UniText::UniTextGenerator::RichTextTag::GetAttribute”: 未找到匹配的重载函数
      E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.h(274,26):
      可能是“std::optional<_Ty> UniText::UniTextGenerator::RichTextTag::GetAttribute(UniText::UniStringView) const”
          E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1429,55):
          “初始化”: 无法从“const UniText::UniString”转换为“UniText::UniStringView”
              E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1429,55):
              没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1430,25): error C2672: “UniText::UniTextGenerator::RichTextTag::GetAttribute”: 未找到匹配的重载函数
      E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.h(274,26):
      可能是“std::optional<_Ty> UniText::UniTextGenerator::RichTextTag::GetAttribute(UniText::UniStringView) const”
          E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1430,59):
          “初始化”: 无法从“const UniText::UniString”转换为“UniText::UniStringView”
              E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1430,59):
              没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1431,26): error C2672: “UniText::UniTextGenerator::RichTextTag::GetAttribute”: 未找到匹配的重载函数
      E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.h(274,26):
      可能是“std::optional<_Ty> UniText::UniTextGenerator::RichTextTag::GetAttribute(UniText::UniStringView) const”
          E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1431,60):
          “初始化”: 无法从“const UniText::UniString”转换为“UniText::UniStringView”
              E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp(1431,60):
              没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
  UniTextGlobal.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/UniTextGlobal.cpp”)
  
  正在生成代码...
  正在编译...
  UniTextProcessor.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/UniTextProcessor.cpp”)
  
  正在生成代码...
