^E:\TRUNK_BASE\YOUNG\UNITEXTGIT\UNITEXTORIGIN\TESTS\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SE:/trunk_base/Young/UniTextGit/UniTextOrigin/Tests -BE:/trunk_base/Young/UniTextGit/UniTextOrigin/build_tests --check-stamp-file E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_tests/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
