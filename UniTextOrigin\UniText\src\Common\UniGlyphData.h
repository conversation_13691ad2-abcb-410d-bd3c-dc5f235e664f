// Some linear algebra、geometry and color definitions are defined in this file.

#ifndef __UniGlyphData_h__
#define __UniGlyphData_h__

#include "UniPlatform.h"

#include <array>
#include <functional>
#include <algorithm>

NAMESPACE_BEGIN

/// <summary>
/// Max texture size is limited to 2048x2048, therefore, we can use smaller dataTypes
/// </summary>
template <class T>
struct Rect
{
    /// <summary>
    /// Rotatable Rect
    /// false: not rotated
    /// true: clockwise 90 degree rotation
    /// </summary>
    //bool isRotated;
    /// (x, y) defines the bottom left corner
    T x;
    T y;
    T width;
    T height;

    Rect() { x = y = width = height = 0; }
    Rect(T _x, T _y, T _width, T _height) : x(_x), y(_y), width(_width), height(_height)
    {
        //isRotated = false;
    }
    Rect(const Rect& other) : x(other.x), y(other.y), width(other.width), height(other.height) {}
    /*
    T operator[] (size_t idx) const
    {
        switch (idx) { case 0: return width; case 1: return height; default: return width; }
    }
    */

    bool operator==(const Rect& other) const {
        return x == other.x && y == other.y && width == other.width && height == other.height;
    }

    T GetAxis(size_t idx) const
    {
        switch (idx) { case 0: return width; case 1: return height; default: return width; }
    }

    T Area() const { return width * height; }
};

// Theoratically uint8 is enough for each sub rect
using GlyphRect = Rect<uint16>;     // Glyph rect in Texture
using GlyphBounds = Rect<float>;    // Glyph bounds to the baseline

/// <summary>
/// A Bits system to optimize memory usage
/// </summary>
template <class T>
struct Bits
{
    T data;
    Bits() { Set(0); }
    Bits(T _data) { Set(_data); }

    const bool HasBit(int bitPosition) const noexcept { return (data & (1 << bitPosition)) != 0; }
    void SetBit(int bitPosition, bool value) noexcept
    {
        if (value) data |= (1 << bitPosition);
        else data &= ~(1 << bitPosition);
    }
    void Set(T _data) noexcept { data = _data; }
};

using Bits8 = Bits<uint8>;
using Bits16 = Bits<uint16>;

template<typename T, size_t length>
struct LargeBits
{
    static inline constexpr int size_in_bits = sizeof(T) * 8;
    static inline constexpr int data_size = length % size_in_bits > 0 ? length / size_in_bits + 1 : length / size_in_bits;

    inline void SetBit(int bitPosition, bool value) noexcept
    {
        if (bitPosition < length)
        {
            int bitIndex = bitPosition / size_in_bits;
            T bit = static_cast<T>(bitPosition % size_in_bits);

            if (value) data[bitIndex] |= (static_cast<T>(1) << bit);
            else data[bitIndex] &= ~(static_cast<T>(1) << bit);
        }
    }

    inline const bool HasBit(int bitPosition) const noexcept
    {
        if (bitPosition < length)
        {
            int bitIndex = bitPosition / size_in_bits;
            T bit = static_cast<T>(bitPosition % size_in_bits);
            return (data[bitIndex] & (static_cast<T>(1) << bit)) != 0;
        }
        return false;
    }

    inline const bool Equal(T val) const
    {
        for (const T& d : data)
        {
            if (d != val) return false;
        }

        return true;
    }

    inline void Clear(T val) { data.fill(val); }
    std::array<T, data_size> data;
};

/// <summary>
/// Mesh related
/// </summary>
template <class T>
struct Vector3;

////////////////////////////////////////////////////////////
// Vector2
////////////////////////////////////////////////////////////
template <class T>
struct Vector2
{
    T x;
    T y;

    Vector2() { x = y = 0; }
    Vector2(T _v) { x = y = _v; }
    Vector2(T _x, T _y) { x = _x; y = _y; }
    Vector2(const Vector3<T>& vec3) { x = vec3.x; y = vec3.y; }
    inline T Length() const noexcept { return std::sqrt(x * x + y * y); }
    inline T SqrLength() const noexcept { return x * x + y * y; }
    inline void Normalized() noexcept {
        T length = SqrLength();
        if (length != 0)
        {
            length = std::sqrt(length);
            x /= length;
            y /= length;
        }
    }

    inline void FastNormalized() noexcept {
        T length = Hypotenuse();
        if (length != 0)
        {
            x /= length;
            y /= length;
        }
    }

    // Alpha max plus beta min algorithm
    // use to approximate Length of a Vector
    inline T Hypotenuse() const noexcept {
        constexpr T alphaMax = 0.9398086351723256, betaMed = 0.38928148272372454;// , gammaMin = 0.2987061876143797;
        auto [min, max] = std::minmax(std::abs(x), std::abs(y));
        return std::max(max, alphaMax * max + betaMed * min);// +gammaMin * z);
    }

    static inline Vector2 Normalize(const Vector2& vec) noexcept {
        Vector2 result = vec;
        // fast normalized produce incorrect artifacts in GPU Rendering
        //result.FastNormalized();
        result.Normalized();
        return result;
    }

    static inline T Dot(const Vector2& lhs, const Vector2& rhs) noexcept {
        return lhs.x * rhs.x + lhs.y * rhs.y;
    }

    static inline Vector2 Right(const Vector2& vec) noexcept {
        return Vector2(vec.y, -vec.x);
    }

    static inline Vector2 Left(const Vector2& vec) noexcept {
        return Vector2(-vec.y, vec.x);
    }

    static inline Vector2 Min(const Vector2& a, const Vector2& b) noexcept {
        return Vector2(std::min(a.x, b.x), std::min(a.y, b.y));
    }

    static inline Vector2 Max(const Vector2& a, const Vector2& b) noexcept {
        return Vector2(std::max(a.x, b.x), std::max(a.y, b.y));
    }

    static inline Vector2 Lerp(const Vector2& a, const Vector2& b, T t) noexcept {
        return a * (1.0f - t) + b * t;
        //return Vector2(a.x + (b.x - a.x) * t, a.y + (b.y - a.y) * t);
    }

    constexpr Vector2& operator-=(const Vector2& rhs) noexcept {
        x -= rhs.x;
        y -= rhs.y;
        return *this;
    }

    constexpr Vector2& operator+=(const Vector2& rhs) noexcept {
        x += rhs.x;
        y += rhs.y;
        return *this;
    }

    constexpr bool operator==(const Vector2& rhs) const noexcept {
        return x == rhs.x && y == rhs.y;
    }

    constexpr Vector2 operator-(const Vector2& rhs) const noexcept {
        return Vector2(x - rhs.x, y - rhs.y);
    }

    constexpr Vector2 operator+(const Vector2& rhs) const noexcept {
        return Vector2(x + rhs.x, y + rhs.y);
    }

    constexpr Vector2 operator*(T scalar) const noexcept {
        return Vector2(x * scalar, y * scalar);
    }

    constexpr Vector2 operator*(const Vector2& v ) const noexcept {
        return Vector2( x * v.x, y * v.y );
    }

    constexpr Vector2 operator/(T scalar) const noexcept {
        static_assert(scalar != 0, "Division by zero");
        return Vector2(x / scalar, y / scalar);
    }

    static Vector2 zero;
};

template<class T>
Vector2<T> Vector2<T>::zero = { 0, 0 };

////////////////////////////////////////////////////////////
// Vector3
////////////////////////////////////////////////////////////
template <class T>
struct Vector3
{
    T x;
    T y;
    T z;

    Vector3() { x = y = z = 0; }
    Vector3(T _x, T _y) { x = _x; y = _y; z = 0; }
    Vector3(T _x, T _y, T _z) { x = _x; y = _y; z = _z; }
    Vector3(const Vector2<T>& vec2) { x = vec2.x; y = vec2.y; z = 0; }

    // Alpha max plus beta min algorithm
    // use to approximate Length of a Vector
    inline T Hypotenuse() const noexcept
    {
        constexpr T alphaMax = 0.9398086351723256, betaMed = 0.38928148272372454, gammaMin = 0.2987061876143797;
        auto [min, max] = std::minmax(std::abs(x), std::abs(y), std::abs(z));
        auto med = std::abs(x) + std::abs(y) + std::abs(z) - (max + min);
        return std::max(max, alphaMax * max + betaMed * med + gammaMin * min);
    }

    bool operator==(const Vector3& other) const {
        return x == other.x && y == other.y && z == other.z;
    }

    static inline Vector3 Min(const Vector3& a, const Vector3& b) noexcept {
        return Vector3(std::min(a.x, b.x), std::min(a.y, b.y), std::min(a.z, b.z));
    }

    static inline Vector3 Max(const Vector3& a, const Vector3& b) noexcept {
        return Vector3(std::max(a.x, b.x), std::max(a.y, b.y), std::max(a.z, b.z));
    }

    static Vector3 zero;
};

template<class T>
Vector3<T> Vector3<T>::zero = { 0, 0, 0 };

////////////////////////////////////////////////////////////
// Vector4
////////////////////////////////////////////////////////////
template <class T>
struct Vector4
{
    T x;
    T y;
    T z;
    T w;

    Vector4() { x = y = z = w = 0; }
    Vector4(T _x, T _y, T _z) { x = _x; y = _y; z = _z; w = 0; }
    Vector4(T _x, T _y, T _z, T _w) { x = _x; y = _y; z = _z; w = _w; }
    Vector4(const Vector3<T>& vec3) { x = vec3.x; y = vec3.y; z = vec3.z; w = 0; }

    bool operator==(const Vector4& other) const {
        return x == other.x && y == other.y && z == other.z && w = other.w;
    }

    static Vector4 zero;
};

template<class T>
Vector4<T> Vector4<T>::zero = { 0, 0, 0, 0 };

using Vector2f = Vector2<float>;
using Vector3f = Vector3<float>;
using Vector4f = Vector4<float>;
using TextureCoordi = Vector2<uint16>;

////////////////////////////////////////////////////////////
// Matrix
////////////////////////////////////////////////////////////
template <class T>
struct Matrix3x2
{
    Matrix3x2() { data[0][0] = data[0][1] = data[1][0] = data[1][1] = data[2][0] = data[2][1] = 0; }
    Matrix3x2(T v) {
        data[0][0] = v; data[0][1] = 0;
        data[1][0] = 0; data[1][1] = v;
        data[2][0] = 0; data[2][1] = 0;
    }
    Matrix3x2(Vector2<T> v0, Vector2<T> v1, Vector2<T> v2) {
        data[0][0] = v0.x; data[0][1] = v0.y;
        data[1][0] = v1.x; data[1][1] = v1.y;
        data[2][0] = v2.x; data[2][1] = v2.y;
    }
    Matrix3x2(T m11, T m12, T m21, T m22, T dx, T dy) {
        data[0][0] = m11; data[0][1] = m12;
        data[1][0] = m21; data[1][1] = m22;
        data[2][0] = dx;  data[2][1] = dy;
    }

    // indexer
    template <size_t idx>
    inline Vector2<T> Dim() const noexcept
    {
        static_assert(idx >= 0 && idx < 3, "Index out of range");
        return Vector2<T>(data[idx][0], data[idx][1]);
    }

    inline Matrix3x2 operator *(const Matrix3x2& rhs) const {
        Matrix3x2 ret;
        ret.data[0][0] = data[0][0] * rhs.data[0][0] + data[0][1] * rhs.data[1][0];
        ret.data[0][1] = data[0][0] * rhs.data[0][1] + data[0][1] * rhs.data[1][1];
        ret.data[1][0] = data[1][0] * rhs.data[0][0] + data[1][1] * rhs.data[1][0];
        ret.data[1][1] = data[1][0] * rhs.data[0][1] + data[1][1] * rhs.data[1][1];
        ret.data[2][0] = data[2][0] * rhs.data[0][0] + data[2][1] * rhs.data[1][0] + rhs.data[2][0];
        ret.data[2][1] = data[2][0] * rhs.data[0][1] + data[2][1] * rhs.data[1][1] + rhs.data[2][1];
        return ret;
    }

    inline Matrix3x2 operator *(T rhs) const {
        Matrix3x2 ret;
        ret.data[0][0] = data[0][0] * rhs;
        ret.data[0][1] = data[0][1] * rhs;
        ret.data[1][0] = data[1][0] * rhs;
        ret.data[1][1] = data[1][1] * rhs;
        ret.data[2][0] = data[2][0] * rhs;
        ret.data[2][1] = data[2][1] * rhs;
        return ret;
    }

    inline Vector2<T> operator *(const Vector2<T>& rhs) const {
        return Vector2<T>(data[0][0] * rhs.x + data[0][1] * rhs.y + data[2][0],
                          data[1][0] * rhs.x + data[1][1] * rhs.y + data[2][1]);
    }

    inline T Determinant() const {
        return data[0][0] * data[1][1] - data[0][1] * data[1][0];
    }

    inline Matrix3x2 Invert() const {
        T det = Determinant();
        if (det == 0) return Matrix3x2();

        T invDet = (T)1.0 / det;
        Matrix3x2 ret;
        ret.data[0][0] = data[1][1] * invDet;    ret.data[0][1] = -data[0][1] * invDet;
        ret.data[1][0] = -data[1][0] * invDet;   ret.data[1][1] = data[0][0] * invDet;

        ret.data[2][0] = (data[1][0] * data[2][1] - data[1][1] * data[2][0]) * invDet;
        ret.data[2][1] = (data[0][1] * data[2][0] - data[0][0] * data[2][1]) * invDet;
        return ret;
    }

public:
    T data[3][2];
};

using Matrix3x2f = Matrix3x2<float>;

////////////////////////////////////////////////////////////
/// Bits define
////////////////////////////////////////////////////////////
#define DEFINE_BITS_PROPERTY(PROP_NAME, PROP_INDEX, BITS_OBJECT, POST_CALL) \
inline const bool Has##PROP_NAME() noexcept { return BITS_OBJECT.HasBit(PROP_INDEX); } \
inline void Enable##PROP_NAME(bool enabled) noexcept { BITS_OBJECT.SetBit(PROP_INDEX, enabled); POST_CALL; }

#define DEFINE_BITS_DIRTY(PROP_NAME, PROP_INDEX, BITS_OBJECT) \
inline const bool Is##PROP_NAME##Dirty() noexcept { return BITS_OBJECT.HasBit(PROP_INDEX); } \
inline void Set##PROP_NAME##Dirty(bool enabled) noexcept { BITS_OBJECT.SetBit(PROP_INDEX, enabled); }

/*
/// <summary>
/// Glyph Indexer
/// </summary>
struct GlyphEntry
{
    /// <summary>
    /// last used font size(already rounded), use uint8 to save memory usage, because font size won't exceed 256.<SizePage>
    /// </summary>
    uint8 fontSize;
    uint16 unicode;

    GlyphEntry() : fontSize(16), unicode(0) {}
    GlyphEntry(int font_size, uint16 c) : fontSize(static_cast<uint8>(font_size)), unicode(c) {}

    // for std::map
    bool operator < (const GlyphEntry& b) const noexcept
    {
        // https://quick-bench.com/q/LI-AK_dFZIhuu7qLYw52DeY_cr8
        return (fontSize << 16 | unicode) < (b.fontSize << 16 | b.unicode);
        //if (unicode != b.unicode) return unicode < b.unicode;
        //if (fontSize != b.fontSize) return fontSize < b.fontSize;
        //return false;
    }
    bool operator == (const GlyphEntry& b) const noexcept
    {
        return unicode == b.unicode && fontSize == b.fontSize;
    }
};

// for std::unordered_map
struct GlyphEntryHasher
{
    std::size_t operator()(const GlyphEntry& entry) const noexcept
    {
        return std::hash<int>()(entry.fontSize) << 16 | std::hash<uint16>()(entry.unicode);
    }
};

struct GlyphPageEntry
{
    /// <summary>
    /// Which page the glyph is in
    /// </summary>
    short pageIndex{ -1 };
    /// <summary>
    /// Which tile the glyph is in on the given page
    /// </summary>
    short tileIndex{ -1 };
};
*/

////////////////////////////////////////////////////////////
/// Glyph & Font related
////////////////////////////////////////////////////////////

/// <summary>
/// Bin pack methods
/// </summary>
enum class PackingMethod : char
{
    Grid = 0,
    MaxRects,
    Shelf,
    // not supported
    //Skyline,
    //Guillotine
};

enum class PackingMode : char
{
    /////////////////////////
    // Max Rects packing mode
    BestShortSideFit = 0,
    BestLongSideFit,
    BestAreaFit,
    BottomLeftRule,
    ContactPointRule,
    /////////////////////////
};

enum class RenderMode : char
{
    Smooth,
    SmoothHinted,
    Raster,
    SDF,        // SDF(from outline to sdf), by freetype, will be deprecated
    BSDF,       // BSDF(from bitmap to sdf) by freetype
    CSDF,       // CSDF(custom SDF generation)
    VSDF,       // VSDF(Vector SDF generation), will be deprecated

    GSDF,       // GPU SDF

    //MSDF,     // considering...

    NotSpecified
};

enum class TextOverflow : char
{
    Wrap,       // Wrap text to the next line, only for horizontal 
    Overflow,
    Truncate,
    Ellipse
};

enum class FontStyle : char
{
    Regular,
    Italic,
    Bold,
    BoldItalic
};

enum class TextAlignment : char
{
    // Top, Middle, Bottom - for vertical
    Left, Center, Right
};

enum class RichTextTagType : char
{
    Unknown = -1,
    Color,
    Style,
    Size,           // font size
    Stroke,
    Underline,
    Deleteline,
    Quad,          // not supported yet
    Material,       // not supported yet
    Nobr,

    Max
};

// Not used
// enum class GlyphLoadErrorCode : char
// {
//     OK,
//     FaceInvalid,
//     GlyphNotFound,
//     GlyphNotRendered,
// };

struct GlyphLoadParam
{
    uint16 character;
    int fontSize;
    //bool allowSystemFallbacks;
    RenderMode renderMode;
};

struct GlyphInfo
{
    //Bits8       flags;              // glyph flags: is fallbacked, is rotated, etc
    int8        spreadSize {0};     // sdf spread size: [min=2, max=32]
    short       usedCount {0};      // glyph reference counter
    short       pageIndex{-1};      // which page glyph belong to
    uint32      glyphIndex {0};     // glyph index in the font face
    Vector2f    advance;            // glyph advance
    GlyphRect   rect;               // glyph rect in texture entry
    GlyphBounds bounds;             // glyph bounds to the baseline
};

/// <summary>
/// Properties for font
/// </summary>
struct FontProperties
{
    PackingMethod   packingMethod;            // How the rects are packed
    RenderMode      renderMode;
    uint8           minFontSize;              // Minimun font size that will be used. by default: 16
    uint8           maxFontSize;              // Maximum font size that will be used. by default: 72
    uint8           padding;                  // glyph padding in atlas
    bool            allowRotation;
    uint16          defaultTextureWidth;      // default width when creating a new Texture
    uint16          defaultTextureHeight;      // default height when creating a new Texture
    uint32          replacementChar;          // Unicode codepoint for replacement character

    FontProperties() :
        packingMethod(PackingMethod::Shelf),
        //packingMode(PackingMode::BestShortSideFit),
        renderMode(RenderMode::Smooth),
        minFontSize(14),
        maxFontSize(96),
        padding(1),
        allowRotation(false),
        defaultTextureWidth(256),
        defaultTextureHeight(256),
        replacementChar(0x0000FFFD)  // U+FFFD REPLACEMENT CHARACTER
    {}
};

////////////////////////////////////////////////////////////
// Color
////////////////////////////////////////////////////////////
struct Color
{
public:
    Color()
    {
        r = g = b = 0; a = 255;
    }

    Color(uint8 red, uint8 green, uint8 blue, uint8 alpha = 255) :
        r(red),g(green),b(blue),a(alpha)
    {}

    Color(uint32 color32) :
        r((color32 & 0xff000000) >> 24),
        g((color32 & 0x00ff0000) >> 16),
        b((color32 & 0x0000ff00) >> 8),
        a((color32 & 0x000000ff) >> 0)
    {}

    Color(const Color& other)
    {
        r = other.r; g = other.g; b = other.b; a = other.a;
    }

    bool operator == (const Color& rhs) noexcept
    {
        return this->r == rhs.r && this->g == rhs.g && this->b == rhs.b && this->a == rhs.a;
    }

    bool operator != (const Color& rhs) noexcept
    {
        return this->r != rhs.r || this->g != rhs.g || this->b != rhs.b || this->a != rhs.a;
    }

    uint8& operator[](size_t idx)  noexcept { switch (idx) { case 0: return r; case 1: return g; case 2: return b; case 3: return a; default:return a; } }
    const uint8& operator[](size_t idx) const noexcept { switch (idx) { case 0: return r; case 1: return g; case 2: return b; case 3: return a; default:return a; } }

    uint32 ToUInt32() const noexcept
    {
        return (r << 24) | (g << 16) | (b << 8) | a;
    }

    ////////////////////////////////////////////////////////////
    // Member
    ////////////////////////////////////////////////////////////
    uint8 r, g, b, a;

    ////////////////////////////////////////////////////////////
    // Predefined
    ////////////////////////////////////////////////////////////
    static UNITEXT_API const Color black;
    static UNITEXT_API const Color white;
    static UNITEXT_API const Color red;
    static UNITEXT_API const Color green;
    static UNITEXT_API const Color blue;
};

NAMESPACE_END

#endif // __UniGlyphData_h__