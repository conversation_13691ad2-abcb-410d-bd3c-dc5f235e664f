^E:\TRUNK_BASE\YOUNG\UNITEXTGIT\UNITEXTORIGIN\BUILD_TESTS\CMAKEFILES\862FCAC670B27A60722566F88A3490C9\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SE:/trunk_base/Young/UniTextGit/UniTextOrigin/Tests -BE:/trunk_base/Young/UniTextGit/UniTextOrigin/build_tests --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/trunk_base/Young/UniTextGit/UniTextOrigin/build_tests/UniTextTests.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
