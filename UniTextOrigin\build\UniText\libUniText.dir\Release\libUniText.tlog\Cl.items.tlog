E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\MaxRectsGlyphPacker.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\MaxRectsGlyphPacker.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphPacker\UniGlyphPacker.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniGlyphPacker.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniSDF_Renderer.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniSDF_Renderer.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\GlyphRender\UniFT_Renderer.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniFT_Renderer.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\UniPostProcessor.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniPostProcessor.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Postprocessor\SDFGenerator.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\SDFGenerator.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Windows\WindowsFontFallback.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\WindowsFontFallback.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Android\AndroidFontFallback.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\AndroidFontFallback.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Apple\AppleFontFallback.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\AppleFontFallback.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\System\Default\DefaultFontFallback.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\DefaultFontFallback.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\Unicode\Unicode.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\Unicode.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\FreeType\UniFontFreeType.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniFontFreeType.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\TextShaping\UniTextShaper.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniTextShaper.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextProcessor.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniTextProcessor.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\Custom\UniCustomFont.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniCustomFont.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFont.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniFont.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFontAtlasEntry.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniFontAtlasEntry.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Font\UniFontFallback.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniFontFallback.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniGlyphData.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniGlyphData.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniPlatform.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniPlatform.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniTexture.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniTexture.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniGUID.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniGUID.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\Common\UniObject.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniObject.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextAPIExport.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniTextAPIExport.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGenerator.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniTextGenerator.obj
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTextGlobal.cpp;E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\libUniText.dir\Release\UniTextGlobal.obj
